<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المقارنة - النسخة العربية vs الإنجليزية</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        :root {
            --primary-brown: #A66C42;
            --deep-green: #216354;
            --accent-gold: #D4AF37;
            --warm-cream: #FDF6E3;
            --white: #FFFFFF;
            --black: #1A1A1A;
            --light-gray: #F8F8F8;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--warm-cream);
            color: var(--black);
            line-height: 1.7;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(166, 108, 66, 0.12);
        }

        .header h1 {
            color: var(--primary-brown);
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .comparison-table {
            background: var(--white);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(166, 108, 66, 0.12);
            margin-bottom: 2rem;
        }

        .table-header {
            background: var(--primary-brown);
            color: var(--white);
            padding: 1.5rem;
            text-align: center;
            font-size: 1.3rem;
            font-weight: bold;
        }

        .table-content {
            padding: 0;
        }

        .comparison-row {
            display: grid;
            grid-template-columns: 2fr 1fr 2fr 2fr 1fr;
            border-bottom: 1px solid #eee;
            align-items: center;
            min-height: 60px;
        }

        .comparison-row:last-child {
            border-bottom: none;
        }

        .comparison-row:nth-child(even) {
            background: var(--light-gray);
        }

        .cell {
            padding: 1rem;
            text-align: center;
        }

        .cell.section-name {
            text-align: right;
            font-weight: 600;
            color: var(--primary-brown);
        }

        .status-icon {
            font-size: 1.5rem;
        }

        .status-match {
            color: var(--success);
        }

        .status-partial {
            color: var(--warning);
        }

        .status-missing {
            color: var(--danger);
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .summary-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(166, 108, 66, 0.12);
        }

        .summary-card.success {
            border-top: 4px solid var(--success);
        }

        .summary-card.warning {
            border-top: 4px solid var(--warning);
        }

        .summary-card.danger {
            border-top: 4px solid var(--danger);
        }

        .summary-card h3 {
            color: var(--primary-brown);
            margin-bottom: 1rem;
        }

        .summary-card .number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .issues-section {
            background: var(--white);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(166, 108, 66, 0.12);
            margin: 2rem 0;
        }

        .issues-section h2 {
            color: var(--primary-brown);
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .issue-item {
            background: var(--light-gray);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            border-right: 4px solid var(--warning);
        }

        .issue-item.critical {
            border-right-color: var(--danger);
        }

        .issue-item h4 {
            color: var(--primary-brown);
            margin-bottom: 0.5rem;
        }

        .recommendations {
            background: linear-gradient(135deg, var(--primary-brown), var(--deep-green));
            color: var(--white);
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }

        .recommendations h2 {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .recommendations ul {
            list-style: none;
            padding: 0;
        }

        .recommendations li {
            padding: 0.5rem 0;
            padding-right: 2rem;
            position: relative;
        }

        .recommendations li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: var(--accent-gold);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 تقرير المقارنة الشامل</h1>
            <p>مقارنة تفصيلية بين النسخة العربية والإنجليزية لموقع مطعم أوكتا</p>
        </div>

        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card success">
                <h3>الأقسام المطابقة</h3>
                <div class="number success">85%</div>
                <p>معظم الأقسام متطابقة</p>
            </div>
            <div class="summary-card warning">
                <div class="number warning">3</div>
                <h3>اختلافات طفيفة</h3>
                <p>تحتاج تعديلات بسيطة</p>
            </div>
            <div class="summary-card danger">
                <div class="number danger">2</div>
                <h3>مشاكل مهمة</h3>
                <p>تحتاج إصلاح فوري</p>
            </div>
        </div>

        <!-- Detailed Comparison Table -->
        <div class="comparison-table">
            <div class="table-header">
                مقارنة تفصيلية للأقسام والمميزات
            </div>
            <div class="table-content">
                <div class="comparison-row">
                    <div class="cell section-name"><strong>القسم/الميزة</strong></div>
                    <div class="cell"><strong>العربية</strong></div>
                    <div class="cell"><strong>الإنجليزية</strong></div>
                    <div class="cell"><strong>الملاحظات</strong></div>
                    <div class="cell"><strong>الحالة</strong></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Loading Screen</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">متطابق تماماً</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Navigation Bar</div>
                    <div class="cell">✓ 6 روابط</div>
                    <div class="cell">✓ 6 روابط</div>
                    <div class="cell">نفس الروابط، نصوص مختلفة</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Hero Section</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">نفس التصميم، محتوى مترجم</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">About Section</div>
                    <div class="cell">✓ مفصل</div>
                    <div class="cell">✓ مبسط</div>
                    <div class="cell">النسخة العربية أكثر تفصيلاً</div>
                    <div class="cell"><i class="fas fa-exclamation-triangle status-partial"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Menu Section</div>
                    <div class="cell">✓ + زر PDF</div>
                    <div class="cell">✓ + زر Contact</div>
                    <div class="cell">أزرار مختلفة للقائمة الكاملة</div>
                    <div class="cell"><i class="fas fa-exclamation-triangle status-partial"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Gallery Section</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">متطابق تماماً</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Branches Section</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">نفس المعلومات</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Contact Section</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">نفس النموذج والمعلومات</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Footer</div>
                    <div class="cell">✓ مفصل</div>
                    <div class="cell">✓ مفصل</div>
                    <div class="cell">نفس المعلومات مترجمة</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Language Toggle</div>
                    <div class="cell">✓ English</div>
                    <div class="cell">✓ العربية</div>
                    <div class="cell">يعمل في الاتجاهين</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Dark Mode</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">يعمل في كلا النسختين</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Mobile Menu</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">✓ موجود</div>
                    <div class="cell">متجاوب في كلا النسختين</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>

                <div class="comparison-row">
                    <div class="cell section-name">Skip Link</div>
                    <div class="cell">✗ محذوف</div>
                    <div class="cell">✗ غير موجود</div>
                    <div class="cell">تم حذفه من العربية كما طُلب</div>
                    <div class="cell"><i class="fas fa-check-circle status-match"></i></div>
                </div>
            </div>
        </div>

        <!-- Issues Section -->
        <div class="issues-section">
            <h2>🚨 المشاكل المكتشفة</h2>
            
            <div class="issue-item">
                <h4>1. اختلاف في قسم About</h4>
                <p><strong>المشكلة:</strong> النسخة العربية تحتوي على تفاصيل أكثر وإحصائيات مختلفة</p>
                <p><strong>التأثير:</strong> تجربة مستخدم غير متسقة</p>
            </div>

            <div class="issue-item">
                <h4>2. أزرار القائمة الكاملة مختلفة</h4>
                <p><strong>المشكلة:</strong> العربية تستخدم رابط PDF، الإنجليزية تستخدم رابط Contact</p>
                <p><strong>التأثير:</strong> وظائف مختلفة للميزة نفسها</p>
            </div>

            <div class="issue-item critical">
                <h4>3. محتوى Hero مختلف قليلاً</h4>
                <p><strong>المشكلة:</strong> النصوص ليست ترجمة مباشرة</p>
                <p><strong>التأثير:</strong> رسائل تسويقية مختلفة</p>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="recommendations">
            <h2>💡 التوصيات للإصلاح</h2>
            <ul>
                <li>توحيد محتوى قسم About بين النسختين</li>
                <li>توحيد آلية عرض القائمة الكاملة</li>
                <li>مراجعة ترجمة النصوص في Hero Section</li>
                <li>إضافة نفس الإحصائيات في كلا النسختين</li>
                <li>التأكد من تطابق جميع الروابط والوظائف</li>
                <li>اختبار شامل للتأكد من عمل جميع الميزات</li>
            </ul>
        </div>

        <!-- Final Status -->
        <div style="background: var(--success); color: white; padding: 2rem; border-radius: 15px; text-align: center;">
            <h2>✅ التقييم العام</h2>
            <p style="font-size: 1.2rem; margin: 1rem 0;">
                <strong>85% من الأقسام والمميزات متطابقة</strong>
            </p>
            <p>
                الموقع في حالة جيدة جداً مع اختلافات طفيفة تحتاج إصلاح بسيط
            </p>
        </div>
    </div>
</body>
</html>
