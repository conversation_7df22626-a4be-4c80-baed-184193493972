/* ===== CSS RESET & BASE STYLES ===== */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
    transition: all 0.3s ease;
}

/* Arabic Font Support */
body[data-lang="ar"] {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
    /* Colors */
    --primary-color: #d4af37;
    --primary-dark: #b8941f;
    --primary-light: #e6c866;
    --secondary-color: #8b4513;
    --accent-color: #ff6b35;
    
    /* Text Colors */
    --text-primary: #2c2c2c;
    --text-secondary: #666666;
    --text-light: #999999;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #1a1a1a;
    --bg-overlay: rgba(0, 0, 0, 0.7);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-overlay: linear-gradient(135deg, rgba(212, 175, 55, 0.9) 0%, rgba(139, 69, 19, 0.9) 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    --spacing-3xl: 6rem;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-Index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    
    /* Container */
    --container-max-width: 1200px;
    --container-padding: 1rem;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-light: #999999;
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-dark: #000000;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.section {
    padding: var(--spacing-3xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-subtitle {
    display: inline-block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--spacing-sm);
}

.section-title {
    font-family: 'Playfair Display', serif;
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: var(--line-height-tight);
}

body[data-lang="ar"] .section-title {
    font-family: 'Amiri', serif;
}

.section-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: var(--line-height-relaxed);
}

.highlight {
    color: var(--primary-color);
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: 1px;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--text-light);
}

.btn-secondary:hover {
    background: var(--text-primary);
    color: var(--text-white);
    border-color: var(--text-primary);
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.loading-logo img {
    animation: pulse 2s infinite;
}

.loading-logo h2 {
    font-family: 'Playfair Display', serif;
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

body[data-lang="ar"] .loading-logo h2 {
    font-family: 'Amiri', serif;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--bg-secondary);
    border-top: 3px solid var(--primary-color);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-content p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

[data-theme="dark"] .navbar {
    background: rgba(26, 26, 26, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .navbar.scrolled {
    background: rgba(26, 26, 26, 0.98);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding-left: var(--container-padding);
    padding-right: var(--container-padding);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    transition: transform var(--transition-normal);
}

.nav-logo:hover {
    transform: scale(1.05);
}

.logo-image {
    border-radius: var(--radius-md);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    margin: 0;
    padding: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.lang-toggle,
.theme-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: transparent;
    border: 1px solid var(--text-light);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.lang-toggle:hover,
.theme-toggle:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/hero-bg.jpg') center/cover no-repeat;
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    z-index: -1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    width: 100%;
}

.hero-text {
    z-index: 1;
}

.hero-subtitle {
    display: inline-block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-light);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--spacing-sm);
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: var(--font-size-6xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-white);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
}

body[data-lang="ar"] .hero-title {
    font-family: 'Amiri', serif;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
    z-index: 1;
}

.hero-image-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.hero-image-container img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.hero-image-container:hover img {
    transform: scale(1.05);
}

.hero-image-accent {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    opacity: 0.8;
    z-index: -1;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}

.scroll-arrow {
    color: var(--text-white);
    font-size: var(--font-size-xl);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== ANIMATION CLASSES ===== */
.fade-in {
    opacity: 0;
    animation: fadeIn 1s ease forwards;
}

.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    animation: fadeInLeft 1s ease forwards;
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    animation: fadeInRight 1s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-5xl);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .nav-controls {
        gap: var(--spacing-xs);
    }
    
    .lang-toggle span,
    .theme-toggle span {
        display: none;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .section {
        padding: var(--spacing-2xl) 0;
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 480px) {
    :root {
        --container-padding: 1rem;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
        max-width: 280px;
    }
}

/* ===== ABOUT SECTION ===== */
.about {
    background: var(--bg-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-story h3 {
    font-family: 'Playfair Display', serif;
    font-size: var(--font-size-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

body[data-lang="ar"] .about-story h3 {
    font-family: 'Amiri', serif;
}

.about-story p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.about-features {
    margin-top: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.feature-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: var(--font-size-lg);
}

.feature-content h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.feature-content p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.about-image-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    height: 500px;
}

.about-image-grid img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.about-image-grid img:hover {
    transform: scale(1.02);
}

.main-image {
    grid-row: 1 / 3;
}

.secondary-image {
    align-self: end;
}

/* ===== MENU SECTION ===== */
.menu-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-2xl);
}

.category-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: transparent;
    border: 2px solid var(--text-light);
    border-radius: var(--radius-full);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.category-btn:hover,
.category-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.menu-item {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    opacity: 1;
    transform: scale(1);
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.menu-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.menu-item-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.menu-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.menu-item:hover .menu-item-image img {
    transform: scale(1.1);
}

.menu-item-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
}

.menu-item-content {
    padding: var(--spacing-lg);
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.menu-item-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.menu-item-price {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    flex-shrink: 0;
}

.menu-item-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.menu-item-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.menu-tag {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.menu-item-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.menu-item-actions .btn {
    flex: 1;
    justify-content: center;
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm);
}

/* ===== GALLERY SECTION ===== */
.gallery {
    background: var(--bg-secondary);
}

.gallery-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-2xl);
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: transparent;
    border: 2px solid var(--text-light);
    border-radius: var(--radius-full);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    cursor: pointer;
    opacity: 1;
    transform: scale(1);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.gallery-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-overlay);
    opacity: 0;
    transition: opacity var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-lg);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay h4 {
    color: var(--text-white);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-sm);
}

.gallery-overlay p {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-sm);
}

.gallery-overlay .btn {
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-md);
}

/* ===== LOCATIONS SECTION ===== */
.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.location-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.location-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.location-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.location-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.location-card:hover .location-image img {
    transform: scale(1.05);
}

.location-overlay {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
}

.location-status {
    background: var(--accent-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
}

.location-content {
    padding: var(--spacing-lg);
}

.location-content h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.location-info {
    margin-bottom: var(--spacing-lg);
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.info-item i {
    color: var(--primary-color);
    font-size: var(--font-size-base);
    margin-top: 2px;
    flex-shrink: 0;
}

.info-item span {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

.location-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.location-actions .btn {
    flex: 1;
    justify-content: center;
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm);
}

/* ===== CONTACT SECTION ===== */
.contact {
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.contact-details {
    margin-bottom: var(--spacing-xl);
}

.contact-details h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.contact-details p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-full);
    text-decoration: none;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.contact-form {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--text-light);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: all var(--transition-normal);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-input::placeholder {
    color: var(--text-light);
}

.form-submit {
    width: 100%;
    justify-content: center;
    margin-top: var(--spacing-md);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-dark);
    color: var(--text-white);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.footer-social {
    display: flex;
    gap: var(--spacing-sm);
}

.footer-social .social-link {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

.footer-social .social-link:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.footer-section h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-white);
    margin-bottom: var(--spacing-md);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-xs);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-normal);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.footer-contact i {
    color: var(--primary-color);
    width: 16px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-bottom-content p {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
}

.footer-bottom-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-link {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-normal);
}

.footer-link:hover {
    color: var(--primary-color);
}

/* ===== LIGHTBOX ===== */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.lightbox-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: var(--radius-md);
}

.lightbox-caption {
    color: var(--text-white);
    text-align: center;
    margin-top: var(--spacing-md);
    font-size: var(--font-size-base);
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: transparent;
    border: none;
    color: var(--text-white);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    transition: color var(--transition-normal);
}

.lightbox-close:hover {
    color: var(--primary-color);
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    width: 100%;
    justify-content: space-between;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: var(--text-white);
    font-size: var(--font-size-xl);
    padding: var(--spacing-md);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: all var(--transition-normal);
    pointer-events: all;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: var(--primary-color);
}

/* ===== RESPONSIVE DESIGN ADDITIONS ===== */
@media (max-width: 1024px) {
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .about-image-grid {
        height: 400px;
    }

    .locations-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .menu-grid,
    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .menu-categories,
    .gallery-filters {
        flex-direction: column;
        align-items: center;
    }

    .category-btn,
    .filter-btn {
        width: 200px;
        text-align: center;
    }

    .about-image-grid {
        grid-template-columns: 1fr;
        height: auto;
    }

    .about-image-grid img {
        height: 200px;
    }

    .main-image {
        grid-row: auto;
    }

    .location-actions,
    .menu-item-actions {
        flex-direction: column;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .locations-grid {
        grid-template-columns: 1fr;
    }

    .location-card,
    .menu-item {
        margin: 0 var(--spacing-sm);
    }

    .contact-form {
        padding: var(--spacing-lg);
    }

    .lightbox-content {
        max-width: 95vw;
    }

    .lightbox-nav {
        position: static;
        transform: none;
        margin-top: var(--spacing-md);
        justify-content: center;
        gap: var(--spacing-lg);
    }
}

/* ===== NOTIFICATION SYSTEM ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    color: var(--text-white);
    font-weight: var(--font-weight-medium);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-tooltip);
    transform: translateX(400px);
    opacity: 0;
    transition: all var(--transition-normal);
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    background: #10b981;
}

.notification-error {
    background: #ef4444;
}

.notification-info {
    background: var(--primary-color);
}

.notification-warning {
    background: #f59e0b;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--text-white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: var(--z-tooltip);
    transition: top var(--transition-normal);
}

.skip-link:focus {
    top: 6px;
}

/* Focus styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .loading-screen,
    .lightbox,
    .notification {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }

    .hero-background,
    .hero-overlay {
        display: none;
    }

    .hero-title,
    .section-title {
        color: #000 !important;
    }

    .btn {
        border: 1px solid #000;
        background: transparent !important;
        color: #000 !important;
    }

    .footer {
        background: transparent !important;
        color: #000 !important;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.lazy-load {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.lazy-load.loaded {
    opacity: 1;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .loading-spinner {
        animation: none;
    }

    .scroll-arrow {
        animation: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #000000;
        --text-secondary: #333333;
        --bg-primary: #ffffff;
        --bg-secondary: #f0f0f0;
        --primary-color: #0066cc;
        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
    }
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Firefox scrollbar */
html {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--bg-secondary);
}

/* ===== SELECTION STYLES ===== */
::selection {
    background: var(--primary-color);
    color: var(--text-white);
}

::-moz-selection {
    background: var(--primary-color);
    color: var(--text-white);
}

/* ===== ADDITIONAL ANIMATIONS ===== */
@keyframes slideInFromTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromBottom {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.slide-in-top {
    animation: slideInFromTop 0.6s ease forwards;
}

.slide-in-bottom {
    animation: slideInFromBottom 0.6s ease forwards;
}

.scale-in {
    animation: scaleIn 0.4s ease forwards;
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.m-0 { margin: 0; }
.p-0 { padding: 0; }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-md); }
.pt-4 { padding-top: var(--spacing-lg); }

.pb-1 { padding-bottom: var(--spacing-xs); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pb-3 { padding-bottom: var(--spacing-md); }
.pb-4 { padding-bottom: var(--spacing-lg); }

/* ===== FINAL RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 320px) {
    :root {
        --font-size-6xl: 2.5rem;
        --font-size-5xl: 2rem;
        --font-size-4xl: 1.75rem;
        --font-size-3xl: 1.5rem;
    }

    .container {
        padding: 0 0.75rem;
    }

    .section {
        padding: var(--spacing-xl) 0;
    }

    .hero-actions {
        gap: var(--spacing-sm);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}
