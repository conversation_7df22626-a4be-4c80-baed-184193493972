// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced navigation functionality with mobile fixes
    function initNavigation() {
        const navbar = document.getElementById('navbar');
        const mobileToggle = document.querySelector('.mobile-toggle, .hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        // Sticky navbar on scroll
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // Mobile menu toggle with improved functionality
        if (mobileToggle && navMenu) {
            mobileToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                this.classList.toggle('active');
                navMenu.classList.toggle('active');
                document.body.classList.toggle('menu-open');
                
                // Prevent body scroll when menu is open
                if (navMenu.classList.contains('active')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });
            
            // Close mobile menu when clicking on a link
            const navLinks = navMenu.querySelectorAll('a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                    document.body.classList.remove('menu-open');
                    document.body.style.overflow = '';
                });
            });
            
            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!navbar.contains(e.target) && navMenu.classList.contains('active')) {
                    mobileToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                    document.body.classList.remove('menu-open');
                    document.body.style.overflow = '';
                }
            });
            
            // Close menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                    mobileToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                    document.body.classList.remove('menu-open');
                    document.body.style.overflow = '';
                }
            });
            
            // Keyboard accessibility
            mobileToggle.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        }
        
        // Smooth scrolling for navigation links with mobile offset
        const navLinks = document.querySelectorAll('a[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    const isMobile = window.innerWidth <= 768;
                    const offsetTop = targetSection.offsetTop - (isMobile ? 70 : 80);
                    
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768 && navMenu.classList.contains('active')) {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.classList.remove('menu-open');
                document.body.style.overflow = '';
            }
        });
    }
    initNavigation();
    
    // Menu Tabs Functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked button and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Gallery Filter Functionality
    const filterButtons = document.querySelectorAll('.filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Remove active class from all filter buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Show/hide gallery items based on filter
            galleryItems.forEach(item => {
                const category = item.getAttribute('data-category');
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.classList.add('loading');
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Gallery Modal Functionality
    const modal = document.getElementById('mapModal');
    const closeModal = document.querySelector('.close');

    galleryItems.forEach(item => {
        item.addEventListener('click', function() {
            const img = this.querySelector('img');
            const imgSrc = img.src;
            const imgAlt = img.alt;
            
            // Create image element for modal
            const modalImg = document.createElement('img');
            modalImg.src = imgSrc;
            modalImg.alt = imgAlt;
            modalImg.style.width = '100%';
            modalImg.style.height = 'auto';
            modalImg.style.borderRadius = '8px';
            
            // Clear modal content and add image
            const mapContainer = document.getElementById('mapContainer');
            mapContainer.innerHTML = '';
            mapContainer.appendChild(modalImg);
            
            modal.style.display = 'block';
        });
    });

    // Map Modal Functionality for Branches
    const mapButtons = document.querySelectorAll('.view-map');
    
    mapButtons.forEach(button => {
        button.addEventListener('click', function() {
            const location = this.getAttribute('data-location');
            showMap(location);
            modal.style.display = 'block';
        });
    });

    // Close modal functionality
    closeModal.addEventListener('click', function() {
        modal.style.display = 'none';
    });

    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Contact Form Functionality
    const contactForm = document.querySelector('.contact-form');
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(this);
        const name = formData.get('name');
        const email = formData.get('email');
        const phone = formData.get('phone');
        const message = formData.get('message');
        
        // Basic validation
        if (!name || !email || !message) {
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }
        
        if (!isValidEmail(email)) {
            showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
            return;
        }
        
        // Simulate form submission
        showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً', 'success');
        this.reset();
    });

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('loading');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.feature-card, .menu-item, .branch-card, .gallery-item').forEach(el => {
        observer.observe(el);
    });

    // Counter animation for stats (if needed in future)
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        }
        
        updateCounter();
    }

    // Lazy loading for images
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.src; // Trigger loading
                img.classList.add('loading');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
});

// Helper Functions

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button class="notification-close">&times;</button>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        z-index: 3000;
        display: flex;
        align-items: center;
        gap: 10px;
        max-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        .notification-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            margin-left: 10px;
        }
    `;
    
    if (!document.querySelector('#notification-styles')) {
        style.id = 'notification-styles';
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Close functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => removeNotification(notification));
    
    // Auto remove after 5 seconds
    setTimeout(() => removeNotification(notification), 5000);
}

function removeNotification(notification) {
    notification.style.animation = 'slideOut 0.3s ease';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Show map in modal
function showMap(location) {
    const mapContainer = document.getElementById('mapContainer');
    
    // Map coordinates for each branch
    const locations = {
        downtown: {
            name: 'فرع وسط البلد',
            address: '1 شارع طلعت حرب، ميدان التحرير، القاهرة',
            lat: 30.0444,
            lng: 31.2357
        },
        newcairo: {
            name: 'فرع القاهرة الجديدة',
            address: 'التجمع الخامس، ميوز مول، تقاطع شارع التسعين الشمالي مع محور السادات',
            lat: 30.0131,
            lng: 31.4914
        },
        alexandria: {
            name: 'فرع سان ستيفانو',
            address: 'طريق الجيش، سان ستيفانو، أمام فندق 26 يوليو',
            lat: 31.2001,
            lng: 29.9187
        }
    };
    
    const locationData = locations[location];
    
    if (locationData) {
        // Create a simple map placeholder with location info
        mapContainer.innerHTML = `
            <div style="padding: 20px; text-align: center; background: #f8f8f8; border-radius: 8px; height: 100%;">
                <h3 style="color: #8B4513; margin-bottom: 20px;">${locationData.name}</h3>
                <p style="color: #666; margin-bottom: 20px; line-height: 1.6;">${locationData.address}</p>
                <div style="background: #e0e0e0; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <i class="fas fa-map-marker-alt" style="font-size: 3rem; color: #8B4513;"></i>
                </div>
                <p style="color: #666; font-size: 0.9rem;">
                    <strong>إحداثيات:</strong> ${locationData.lat}, ${locationData.lng}
                </p>
                <div style="margin-top: 20px;">
                    <a href="https://www.google.com/maps?q=${locationData.lat},${locationData.lng}" 
                       target="_blank" 
                       style="background: #8B4513; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        فتح في خرائط جوجل
                    </a>
                </div>
            </div>
        `;
    }
}

// Reservation System (Basic)
function makeReservation() {
    // This would typically connect to a backend system
    const name = prompt('الاسم:');
    const phone = prompt('رقم الهاتف:');
    const date = prompt('التاريخ (YYYY-MM-DD):');
    const time = prompt('الوقت (HH:MM):');
    const guests = prompt('عدد الأشخاص:');
    
    if (name && phone && date && time && guests) {
        showNotification(`تم تسجيل طلب الحجز باسم ${name} ليوم ${date} في تمام الساعة ${time} لعدد ${guests} أشخاص. سنتواصل معك قريباً لتأكيد الحجز.`, 'success');
    }
}

// Add reservation button functionality if needed
document.addEventListener('DOMContentLoaded', function() {
    // Add reservation buttons to branch cards if needed
    const branchCards = document.querySelectorAll('.branch-card');
    branchCards.forEach(card => {
        const reservationBtn = document.createElement('button');
        reservationBtn.className = 'btn btn-secondary';
        reservationBtn.textContent = 'احجز طاولة';
        reservationBtn.style.marginTop = '10px';
        reservationBtn.style.width = '100%';
        reservationBtn.addEventListener('click', makeReservation);
        
        // Insert before the map button
        const mapBtn = card.querySelector('.view-map');
        mapBtn.parentNode.insertBefore(reservationBtn, mapBtn);
    });
});

// Performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optimized scroll handler
const optimizedScrollHandler = debounce(function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
    }
}, 10);

// Replace the original scroll handler
window.removeEventListener('scroll', optimizedScrollHandler);
window.addEventListener('scroll', optimizedScrollHandler);

// Add loading states for better UX
function showLoading(element) {
    element.style.opacity = '0.7';
    element.style.pointerEvents = 'none';
}

function hideLoading(element) {
    element.style.opacity = '1';
    element.style.pointerEvents = 'auto';
}

// Initialize tooltips for better accessibility
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

// Enhanced Lazy Loading with WebP Support
function initLazyLoading() {
    // Check WebP support
    function supportsWebP() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }

    // Add WebP support class to body
    if (supportsWebP()) {
        document.body.classList.add('webp-supported');
    } else {
        document.body.classList.add('no-webp');
    }

    // Intersection Observer for lazy loading
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                
                // Add loading animation
                img.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                
                // Load image
                if (img.dataset.src) {
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                }
                
                // Add loaded class when image loads
                img.addEventListener('load', () => {
                    img.classList.add('loaded');
                    img.style.opacity = '1';
                    img.style.transform = 'translateY(0)';
                });
                
                observer.unobserve(img);
            }
        });
    }, {
        rootMargin: '50px 0px',
        threshold: 0.1
    });

    // Observe all lazy images
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');
    lazyImages.forEach(img => {
        // Set initial state
        img.style.opacity = '0';
        img.style.transform = 'translateY(20px)';
        imageObserver.observe(img);
    });
}

// Enhanced Form Validation
function initFormValidation() {
    const form = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const successMessage = document.getElementById('successMessage');
    
    if (!form) return;
    
    // Character counter for message field
    const messageField = document.getElementById('message');
    const charCount = document.getElementById('char-count');
    const charCounter = document.querySelector('.char-counter');
    
    if (messageField && charCount) {
        messageField.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            charCounter.classList.remove('warning', 'danger');
            if (count > 400) {
                charCounter.classList.add('danger');
            } else if (count > 300) {
                charCounter.classList.add('warning');
            }
        });
    }
    
    // Real-time validation
    const inputs = form.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearError(input));
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        let isValid = true;
        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });
        
        if (isValid) {
            submitForm(form, submitBtn, successMessage);
        }
    });
}

function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';
    
    // Clear previous error
    clearError(field);
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        errorMessage = 'هذا الحقل مطلوب';
        isValid = false;
    }
    
    // Specific field validations
    if (value && isValid) {
        switch (fieldName) {
            case 'name':
                if (value.length < 2) {
                    errorMessage = 'الاسم يجب أن يكون أكثر من حرفين';
                    isValid = false;
                } else if (!/^[\u0600-\u06FF\s\w]+$/.test(value)) {
                    errorMessage = 'الاسم يحتوي على أحرف غير صالحة';
                    isValid = false;
                }
                break;
                
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    errorMessage = 'البريد الإلكتروني غير صحيح';
                    isValid = false;
                }
                break;
                
            case 'phone':
                if (value && !/^01[0-9]{9}$/.test(value)) {
                    errorMessage = 'رقم الهاتف يجب أن يكون 11 رقم ويبدأ بـ 01';
                    isValid = false;
                }
                break;
                
            case 'message':
                if (value.length < 10) {
                    errorMessage = 'الرسالة يجب أن تكون أكثر من 10 أحرف';
                    isValid = false;
                } else if (value.length > 500) {
                    errorMessage = 'الرسالة طويلة جداً (الحد الأقصى 500 حرف)';
                    isValid = false;
                }
                break;
                
            case 'privacy':
                if (!field.checked) {
                    errorMessage = 'يجب الموافقة على سياسة الخصوصية';
                    isValid = false;
                }
                break;
        }
    }
    
    if (!isValid) {
        showError(field, errorMessage);
    }
    
    return isValid;
}

function showError(field, message) {
    field.classList.add('error');
    const errorElement = document.getElementById(field.name + '-error');
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }
}

function clearError(field) {
    field.classList.remove('error');
    const errorElement = document.getElementById(field.name + '-error');
    if (errorElement) {
        errorElement.textContent = '';
        errorElement.classList.remove('show');
    }
}

function submitForm(form, submitBtn, successMessage) {
    // Show loading state
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline-flex';
    submitBtn.disabled = true;
    
    // Simulate form submission (replace with actual API call)
    setTimeout(() => {
        // Hide loading state
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        submitBtn.disabled = false;
        
        // Show success message
        successMessage.style.display = 'block';
        form.reset();
        
        // Reset character counter
        const charCount = document.getElementById('char-count');
        if (charCount) charCount.textContent = '0';
        
        // Hide success message after 5 seconds
        setTimeout(() => {
            successMessage.style.display = 'none';
        }, 5000);
    }, 2000);
}

// Menu Search and Filter Functionality
function initMenuSearch() {
    const searchInput = document.getElementById('menuSearch');
    const priceFilter = document.getElementById('priceFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const menuItems = document.querySelectorAll('.menu-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    if (!searchInput || !priceFilter) return;
    
    // Add no results message
    tabContents.forEach(tabContent => {
        const noResults = document.createElement('div');
        noResults.className = 'no-results';
        noResults.innerHTML = `
            <i class="fas fa-search"></i>
            <h3>لا توجد نتائج</h3>
            <p>لم نجد أي أطباق تطابق بحثك. جرب كلمات مختلفة أو امسح الفلاتر.</p>
        `;
        tabContent.appendChild(noResults);
    });
    
    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        filterMenuItems(searchTerm, priceFilter.value);
    });
    
    // Price filter functionality
    priceFilter.addEventListener('change', function() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        filterMenuItems(searchTerm, this.value);
    });
    
    // Clear filters
    clearFiltersBtn.addEventListener('click', function() {
        searchInput.value = '';
        priceFilter.value = '';
        filterMenuItems('', '');
        
        // Add visual feedback
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });
    
    function filterMenuItems(searchTerm, priceRange) {
        const activeTab = document.querySelector('.tab-content.active');
        if (!activeTab) return;
        
        const items = activeTab.querySelectorAll('.menu-item');
        const noResults = activeTab.querySelector('.no-results');
        let visibleCount = 0;
        
        items.forEach(item => {
            const title = item.querySelector('h3')?.textContent.toLowerCase() || '';
            const description = item.querySelector('p')?.textContent.toLowerCase() || '';
            const priceText = item.querySelector('.price')?.textContent || '';
            const price = parseInt(priceText.replace(/[^0-9]/g, '')) || 0;
            
            let matchesSearch = true;
            let matchesPrice = true;
            
            // Search filter
            if (searchTerm) {
                matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
            }
            
            // Price filter
            if (priceRange) {
                switch (priceRange) {
                    case '0-50':
                        matchesPrice = price < 50;
                        break;
                    case '50-100':
                        matchesPrice = price >= 50 && price <= 100;
                        break;
                    case '100-150':
                        matchesPrice = price >= 100 && price <= 150;
                        break;
                    case '150+':
                        matchesPrice = price > 150;
                        break;
                }
            }
            
            // Show/hide item
            if (matchesSearch && matchesPrice) {
                item.classList.remove('hidden');
                item.classList.add('highlight');
                setTimeout(() => item.classList.remove('highlight'), 300);
                visibleCount++;
            } else {
                item.classList.add('hidden');
            }
        });
        
        // Show/hide no results message
        if (visibleCount === 0 && (searchTerm || priceRange)) {
            noResults.classList.add('show');
        } else {
            noResults.classList.remove('show');
        }
    }
}

// Enhanced Maps Integration
function initEnhancedMaps() {
    const mapContainers = document.querySelectorAll('.map-container');
    
    mapContainers.forEach(container => {
        const iframe = container.querySelector('iframe');
        if (!iframe) return;
        
        // Add loading state
        const loader = document.createElement('div');
        loader.className = 'map-loader';
        loader.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تحميل الخريطة...';
        container.appendChild(loader);
        
        // Add directions button
        const directionsBtn = document.createElement('button');
        directionsBtn.className = 'directions-btn';
        directionsBtn.innerHTML = '<i class="fas fa-directions"></i> احصل على الاتجاهات';
        container.appendChild(directionsBtn);
        
        // Handle iframe load
        iframe.addEventListener('load', function() {
            loader.style.display = 'none';
        });
        
        // Handle directions button
        directionsBtn.addEventListener('click', function() {
            const src = iframe.src;
            const match = src.match(/q=([^&]+)/);
            if (match) {
                const location = decodeURIComponent(match[1]);
                const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(location)}`;
                window.open(googleMapsUrl, '_blank');
            }
        });
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initTooltips();
    initLazyLoading();
    initFormValidation();
    initMenuSearch();
    initEnhancedMaps();
});

// PDF Menu Functions
function viewPDFMenu() {
    const pdfViewer = document.getElementById('pdfViewer');
    const pdfPlaceholder = document.getElementById('pdfPlaceholder');
    
    // Show loading state
    showNotification('جاري تحميل المنيو...', 'info');
    
    // Set PDF source
    pdfViewer.src = 'Menu.pdf#toolbar=1&navpanes=1&scrollbar=1';
    
    // Show PDF viewer and hide placeholder
    pdfViewer.style.display = 'block';
    pdfPlaceholder.style.display = 'none';
    
    // Handle load success
    pdfViewer.onload = function() {
        showNotification('تم تحميل المنيو بنجاح!', 'success');
    };
    
    // Handle load error
    pdfViewer.onerror = function() {
        showNotification('حدث خطأ في تحميل المنيو. يرجى المحاولة مرة أخرى.', 'error');
        pdfViewer.style.display = 'none';
        pdfPlaceholder.style.display = 'flex';
    };
}

// Function to hide PDF viewer
function hidePDFMenu() {
    const pdfViewer = document.getElementById('pdfViewer');
    const pdfPlaceholder = document.getElementById('pdfPlaceholder');
    
    pdfViewer.style.display = 'none';
    pdfPlaceholder.style.display = 'flex';
    pdfViewer.src = '';
}

// Function to check if PDF is supported
function isPDFSupported() {
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    
    // On mobile devices, prefer opening in new tab
    if (isMobile) {
        return false;
    }
    
    // Check if browser supports PDF viewing
    return navigator.mimeTypes && navigator.mimeTypes['application/pdf'];
}

// Enhanced PDF viewing with fallback
function viewPDFMenuEnhanced() {
    if (isPDFSupported()) {
        viewPDFMenu();
    } else {
        // Fallback: open in new tab
        window.open('Menu.pdf', '_blank');
        showNotification('تم فتح المنيو في نافذة جديدة', 'info');
    }
}
