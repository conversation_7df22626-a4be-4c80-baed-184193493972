<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improvements Test - OCTA Restaurant</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        :root {
            --primary-brown: #A66C42;
            --deep-green: #216354;
            --primary-hover: #8B5A35;
            --accent-gold: #D4AF37;
            --warm-cream: #FDF6E3;
            --elegant-shadow: rgba(166, 108, 66, 0.12);
            --elegant-border: rgba(166, 108, 66, 0.15);
            --soft-glow: rgba(166, 108, 66, 0.25);
            --white: #FFFFFF;
            --black: #1A1A1A;
            --light-gray: #F8F8F8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--warm-cream);
            color: var(--black);
            line-height: 1.7;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        /* Enhanced Typography for Languages */
        [data-lang="en"] body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            letter-spacing: 0.01em;
            font-size: 1rem;
        }

        [data-lang="ar"] body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
            letter-spacing: 0.02em;
            font-size: 1rem;
            direction: rtl;
            text-align: right;
        }

        /* Improved heading typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: 1rem;
            color: var(--black);
        }

        [data-lang="en"] h1, [data-lang="en"] h2, [data-lang="en"] h3, 
        [data-lang="en"] h4, [data-lang="en"] h5, [data-lang="en"] h6 {
            font-family: 'Playfair Display', Georgia, serif;
            letter-spacing: -0.02em;
            font-weight: 500;
        }

        [data-lang="ar"] h1, [data-lang="ar"] h2, [data-lang="ar"] h3, 
        [data-lang="ar"] h4, [data-lang="ar"] h5, [data-lang="ar"] h6 {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 8px 25px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }

        .header h1 {
            color: var(--primary-brown);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .header h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--accent-gold);
            border-radius: 2px;
        }

        .lang-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .lang-btn {
            background: var(--primary-brown);
            color: var(--white);
            border: 2px solid var(--primary-brown);
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .lang-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-hover);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .lang-btn:hover::before {
            left: 0;
        }

        .lang-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--elegant-shadow);
        }

        .lang-btn.active {
            background: var(--deep-green);
            border-color: var(--deep-green);
        }

        .test-section {
            background: var(--white);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }

        .test-section h2 {
            color: var(--primary-brown);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        /* Navigation Test */
        .nav-test {
            background: var(--light-gray);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo-test {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-brown);
        }

        [data-lang="en"] .logo-text {
            font-family: 'Playfair Display', Georgia, serif;
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        [data-lang="ar"] .logo-text {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: var(--black);
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.95rem;
        }

        [data-lang="en"] .nav-link {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            letter-spacing: 0.01em;
            font-size: 0.95rem;
        }

        [data-lang="ar"] .nav-link {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: var(--elegant-shadow);
            color: var(--primary-brown);
            transform: translateY(-1px);
        }

        /* Typography Test */
        .typography-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .typography-card {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid var(--elegant-border);
        }

        .typography-card h3 {
            color: var(--deep-green);
            margin-bottom: 1rem;
        }

        .sample-text {
            margin: 1rem 0;
            padding: 1rem;
            background: var(--white);
            border-radius: 8px;
            border-left: 4px solid var(--primary-brown);
        }

        /* Button Test */
        .btn-test {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin: 1rem 0;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-size: 1rem;
        }

        [data-lang="en"] .btn {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            letter-spacing: 0.01em;
            font-size: 0.95rem;
        }

        [data-lang="ar"] .btn {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--primary-brown);
            color: var(--white);
            border: 2px solid var(--primary-brown);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-hover);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .btn-primary:hover::before {
            left: 0;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--elegant-shadow);
        }

        .btn-secondary {
            background: transparent;
            color: var(--deep-green);
            border: 2px solid var(--deep-green);
        }

        .btn-secondary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--deep-green);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .btn-secondary:hover::before {
            left: 0;
        }

        .btn-secondary:hover {
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(33, 99, 84, 0.15);
        }

        /* Menu Item Test */
        .menu-item {
            background: var(--white);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px var(--elegant-shadow);
            transition: all 0.3s ease;
            border: 1px solid var(--elegant-border);
            position: relative;
            margin: 1rem 0;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-brown);
            transform: scaleX(0);
            transition: transform 0.3s ease;
            transform-origin: left;
            z-index: 1;
        }

        .menu-item:hover::before {
            transform: scaleX(1);
        }

        .menu-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px var(--elegant-shadow);
            border-color: var(--soft-glow);
        }

        .menu-item-content {
            padding: 1.5rem;
        }

        .menu-item-content h3 {
            color: var(--black);
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
            font-weight: 600;
        }

        [data-lang="en"] .menu-item-content h3 {
            font-family: 'Playfair Display', Georgia, serif;
            font-weight: 600;
            font-size: 1.25rem;
            letter-spacing: -0.01em;
        }

        [data-lang="ar"] .menu-item-content h3 {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .menu-price {
            color: var(--deep-green);
            font-weight: bold;
            font-size: 1.1rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .status-card {
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            font-weight: bold;
        }

        .status-fixed {
            background: rgba(33, 99, 84, 0.1);
            color: var(--deep-green);
            border: 2px solid var(--deep-green);
        }

        .status-improved {
            background: rgba(166, 108, 66, 0.1);
            color: var(--primary-brown);
            border: 2px solid var(--primary-brown);
        }
    </style>
</head>
<body data-lang="en">
    <div class="container">
        <div class="header">
            <h1 id="main-title">🔧 Improvements Test</h1>
            <p id="main-description">Testing all the fixes and improvements made to OCTA Restaurant</p>
        </div>

        <div class="lang-controls">
            <button class="lang-btn active" onclick="setLanguage('en')" id="en-btn">
                <i class="fas fa-flag-usa"></i> English
            </button>
            <button class="lang-btn" onclick="setLanguage('ar')" id="ar-btn">
                <i class="fas fa-flag"></i> العربية
            </button>
        </div>

        <div class="test-section">
            <h2 id="fixes-title">🐛 Issues Fixed</h2>
            <div class="status-grid">
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle"></i>
                    <div id="fix1">Mixed Language Text</div>
                </div>
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle"></i>
                    <div id="fix2">Navigation Typography</div>
                </div>
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle"></i>
                    <div id="fix3">Font Sizes</div>
                </div>
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle"></i>
                    <div id="fix4">Button Typography</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 id="nav-title">🧭 Navigation Test</h2>
            <div class="nav-test">
                <div class="logo-test">
                    <i class="fas fa-utensils" style="color: var(--primary-brown); font-size: 1.5rem;"></i>
                    <span class="logo-text">OCTA</span>
                </div>
                <div class="nav-links">
                    <a href="#" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span id="nav-home">Home</span>
                    </a>
                    <a href="#" class="nav-link">
                        <i class="fas fa-utensils"></i>
                        <span id="nav-menu">Menu</span>
                    </a>
                    <a href="#" class="nav-link">
                        <i class="fas fa-envelope"></i>
                        <span id="nav-contact">Contact</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 id="typography-title">📝 Typography Test</h2>
            <div class="typography-grid">
                <div class="typography-card">
                    <h3 id="headings-title">Headings</h3>
                    <h1 id="sample-h1">Main Title</h1>
                    <h2 id="sample-h2">Section Title</h2>
                    <h3 id="sample-h3">Subsection</h3>
                </div>
                <div class="typography-card">
                    <h3 id="paragraph-title">Paragraphs</h3>
                    <div class="sample-text">
                        <p id="sample-paragraph">Experience authentic Italian cuisine at OCTA Restaurant. Our passionate chefs bring traditional recipes with modern presentation.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 id="buttons-title">🔘 Buttons Test</h2>
            <div class="btn-test">
                <button class="btn btn-primary">
                    <i class="fas fa-utensils"></i>
                    <span id="btn-menu">Explore Menu</span>
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-phone"></i>
                    <span id="btn-contact">Contact Us</span>
                </button>
            </div>
        </div>

        <div class="test-section">
            <h2 id="menu-items-title">🍽️ Menu Items Test</h2>
            <div class="menu-item">
                <div class="menu-item-content">
                    <h3 id="dish-name">Margherita Pizza</h3>
                    <p id="dish-desc">Classic Italian pizza with fresh tomatoes, mozzarella, and basil</p>
                    <div class="menu-price" id="dish-price">120 EGP</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 id="improvements-title">✨ Improvements Made</h2>
            <div class="status-grid">
                <div class="status-card status-improved">
                    <i class="fas fa-font"></i>
                    <div id="improvement1">Enhanced Typography</div>
                </div>
                <div class="status-card status-improved">
                    <i class="fas fa-language"></i>
                    <div id="improvement2">Bilingual Support</div>
                </div>
                <div class="status-card status-improved">
                    <i class="fas fa-mobile-alt"></i>
                    <div id="improvement3">Responsive Design</div>
                </div>
                <div class="status-card status-improved">
                    <i class="fas fa-palette"></i>
                    <div id="improvement4">Visual Polish</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Language content data
        const content = {
            en: {
                'main-title': '🔧 Improvements Test',
                'main-description': 'Testing all the fixes and improvements made to OCTA Restaurant',
                'fixes-title': '🐛 Issues Fixed',
                'fix1': 'Mixed Language Text',
                'fix2': 'Navigation Typography',
                'fix3': 'Font Sizes',
                'fix4': 'Button Typography',
                'nav-title': '🧭 Navigation Test',
                'nav-home': 'Home',
                'nav-menu': 'Menu',
                'nav-contact': 'Contact',
                'typography-title': '📝 Typography Test',
                'headings-title': 'Headings',
                'sample-h1': 'Main Title',
                'sample-h2': 'Section Title',
                'sample-h3': 'Subsection',
                'paragraph-title': 'Paragraphs',
                'sample-paragraph': 'Experience authentic Italian cuisine at OCTA Restaurant. Our passionate chefs bring traditional recipes with modern presentation.',
                'buttons-title': '🔘 Buttons Test',
                'btn-menu': 'Explore Menu',
                'btn-contact': 'Contact Us',
                'menu-items-title': '🍽️ Menu Items Test',
                'dish-name': 'Margherita Pizza',
                'dish-desc': 'Classic Italian pizza with fresh tomatoes, mozzarella, and basil',
                'dish-price': '120 EGP',
                'improvements-title': '✨ Improvements Made',
                'improvement1': 'Enhanced Typography',
                'improvement2': 'Bilingual Support',
                'improvement3': 'Responsive Design',
                'improvement4': 'Visual Polish'
            },
            ar: {
                'main-title': '🔧 اختبار التحسينات',
                'main-description': 'اختبار جميع الإصلاحات والتحسينات المطبقة على مطعم أوكتا',
                'fixes-title': '🐛 المشاكل المحلولة',
                'fix1': 'النصوص المختلطة',
                'fix2': 'خطوط التنقل',
                'fix3': 'أحجام الخطوط',
                'fix4': 'خطوط الأزرار',
                'nav-title': '🧭 اختبار التنقل',
                'nav-home': 'الرئيسية',
                'nav-menu': 'القائمة',
                'nav-contact': 'اتصل بنا',
                'typography-title': '📝 اختبار الخطوط',
                'headings-title': 'العناوين',
                'sample-h1': 'العنوان الرئيسي',
                'sample-h2': 'عنوان القسم',
                'sample-h3': 'عنوان فرعي',
                'paragraph-title': 'الفقرات',
                'sample-paragraph': 'اختبر المأكولات الإيطالية الأصيلة في مطعم أوكتا. طهاتنا المتمرسون يقدمون الوصفات التقليدية بعرض عصري.',
                'buttons-title': '🔘 اختبار الأزرار',
                'btn-menu': 'استكشف القائمة',
                'btn-contact': 'اتصل بنا',
                'menu-items-title': '🍽️ اختبار عناصر القائمة',
                'dish-name': 'بيتزا مارغريتا',
                'dish-desc': 'بيتزا إيطالية كلاسيكية مع الطماطم الطازجة والموزاريلا والريحان',
                'dish-price': '120 جنيه',
                'improvements-title': '✨ التحسينات المطبقة',
                'improvement1': 'تحسين الخطوط',
                'improvement2': 'الدعم ثنائي اللغة',
                'improvement3': 'التصميم المتجاوب',
                'improvement4': 'التلميع البصري'
            }
        };

        function setLanguage(lang) {
            // Update body attributes
            document.body.setAttribute('data-lang', lang);
            document.documentElement.setAttribute('lang', lang);
            document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');

            // Update button states
            document.getElementById('en-btn').classList.toggle('active', lang === 'en');
            document.getElementById('ar-btn').classList.toggle('active', lang === 'ar');

            // Update all content
            const langContent = content[lang];
            for (const [id, text] of Object.entries(langContent)) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = text;
                }
            }

            // Save preference
            localStorage.setItem('test-language', lang);

            console.log(`Language switched to: ${lang}`);
            console.log('Typography updated for:', lang);
        }

        // Initialize with saved language or default to English
        document.addEventListener('DOMContentLoaded', () => {
            const savedLang = localStorage.getItem('test-language') || 'en';
            setLanguage(savedLang);
        });
    </script>
</body>
</html>
