<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافبار النظيف - مطعم أوكتا</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            background: var(--warm-cream);
            min-height: 100vh;
        }
        
        .test-section {
            background: var(--white);
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }
        
        .test-title {
            color: var(--primary-brown);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            text-align: center;
        }
        
        .navbar-demo {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(166, 108, 66, 0.1);
            border-radius: 15px;
            padding: 1rem 2rem;
            margin: 2rem 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo-demo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .logo-demo img {
            width: 45px;
            height: 45px;
            border-radius: 8px;
        }
        
        .logo-demo span {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-brown);
        }
        
        .nav-links-demo {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
        }
        
        .nav-link-demo {
            padding: 0.75rem 1rem;
            color: var(--black);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            font-weight: 500;
        }
        
        .nav-link-demo:hover {
            background: rgba(166, 108, 66, 0.1);
            color: var(--primary-brown);
        }
        
        .nav-controls-demo {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .control-btn {
            background: var(--primary-brown);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .control-btn:hover {
            background: var(--primary-hover);
            transform: scale(1.1);
        }
        
        .lang-btn {
            background: var(--deep-green);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .lang-btn:hover {
            background: var(--primary-brown);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .comparison-card {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .comparison-card.before {
            border-top: 4px solid #dc3545;
        }
        
        .comparison-card.after {
            border-top: 4px solid var(--deep-green);
        }
        
        .comparison-card h3 {
            color: var(--primary-brown);
            margin-bottom: 1rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .status-card {
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 2px solid;
        }
        
        .status-fixed {
            background: rgba(33, 99, 84, 0.1);
            color: var(--deep-green);
            border-color: var(--deep-green);
        }
        
        .mobile-demo {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 10px;
            margin: 2rem 0;
        }
        
        .mobile-navbar {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(166, 108, 66, 0.1);
            border-radius: 10px;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .mobile-menu-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--primary-brown);
            cursor: pointer;
        }
    </style>
</head>
<body data-lang="ar">
    <div class="test-container">
        <!-- Header -->
        <div class="test-section">
            <h1 class="test-title">🧹 اختبار النافبار النظيف</h1>
            <p style="text-align: center; color: var(--gray); font-size: 1.1rem;">
                تم حذف زر "احجز الآن" من النافبار لحل مشاكل النص المقطوع والخروج عن الصفحة
            </p>
        </div>

        <!-- Current Clean Navbar -->
        <div class="test-section">
            <h2 class="test-title">✨ النافبار النظيف الجديد</h2>
            <div class="navbar-demo">
                <div class="logo-demo">
                    <img src="../images/logo.png" alt="OCTA Logo" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDUiIGhlaWdodD0iNDUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJsb2dvR3JhZCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+PHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I0E2NkM0MiIgLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMyMTYzNTQiIC8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PGNpcmNsZSBjeD0iMjIuNSIgY3k9IjIyLjUiIHI9IjIwIiBmaWxsPSJ1cmwoI2xvZ29HcmFkKSIgc3Ryb2tlPSIjQTY2QzQyIiBzdHJva2Utd2lkdGg9IjIiLz48dGV4dCB4PSI1MCUiIHk9IjU1JSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IlBsYXlmYWlyIERpc3BsYXksIHNlcmlmIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgZm9udC13ZWlnaHQ9ImJvbGQiPk88L3RleHQ+PC9zdmc+'">
                    <span>أوكتا</span>
                </div>
                
                <div class="nav-links-demo">
                    <a href="#" class="nav-link-demo">الرئيسية</a>
                    <a href="#" class="nav-link-demo">عن أوكتا</a>
                    <a href="#" class="nav-link-demo">القائمة</a>
                    <a href="#" class="nav-link-demo">المعرض</a>
                    <a href="#" class="nav-link-demo">الفروع</a>
                    <a href="#" class="nav-link-demo">اتصل بنا</a>
                </div>
                
                <div class="nav-controls-demo">
                    <button class="lang-btn">English</button>
                    <button class="control-btn">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="control-btn mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            <p style="text-align: center; color: var(--deep-green); font-weight: bold;">
                ✅ النافبار الآن نظيف ومتناسق بدون مشاكل
            </p>
        </div>

        <!-- Before vs After Comparison -->
        <div class="test-section">
            <h2 class="test-title">📊 مقارنة قبل وبعد</h2>
            <div class="comparison-grid">
                <div class="comparison-card before">
                    <h3>❌ قبل الحذف</h3>
                    <div style="background: #ffebee; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                        <p style="color: #c62828; font-weight: bold;">مشاكل:</p>
                        <ul style="text-align: right; color: #c62828;">
                            <li>النص مقطوع في العربية</li>
                            <li>الزر خارج الصفحة في الإنجليزية</li>
                            <li>عدم تناسق في التصميم</li>
                            <li>مشاكل في التجاوب</li>
                        </ul>
                    </div>
                </div>
                
                <div class="comparison-card after">
                    <h3>✅ بعد الحذف</h3>
                    <div style="background: rgba(33, 99, 84, 0.1); padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                        <p style="color: var(--deep-green); font-weight: bold;">المزايا:</p>
                        <ul style="text-align: right; color: var(--deep-green);">
                            <li>نافبار نظيف ومتناسق</li>
                            <li>لا توجد مشاكل في النص</li>
                            <li>يعمل على جميع الأجهزة</li>
                            <li>تصميم أنيق ومرتب</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Report -->
        <div class="test-section">
            <h2 class="test-title">📋 تقرير الحالة</h2>
            <div class="status-grid">
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3>النسخة العربية</h3>
                    <p>تم حذف الزر - لا توجد مشاكل في النص</p>
                </div>
                
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3>النسخة الإنجليزية</h3>
                    <p>تم حذف الزر - لا يخرج عن الصفحة</p>
                </div>
                
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3>التجاوب</h3>
                    <p>يعمل مثالياً على جميع الأجهزة</p>
                </div>
                
                <div class="status-card status-fixed">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3>التصميم</h3>
                    <p>نظيف ومتناسق ومرتب</p>
                </div>
            </div>
        </div>

        <!-- Mobile Demo -->
        <div class="test-section">
            <h2 class="test-title">📱 عرض الهواتف المحمولة</h2>
            <div class="mobile-demo">
                <h3 style="color: var(--primary-brown); margin-bottom: 1rem; text-align: center;">النافبار في الهواتف</h3>
                <div class="mobile-navbar">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <img src="../images/logo.png" alt="Logo" style="width: 30px; height: 30px; border-radius: 5px;" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iMTIiIGZpbGw9IiNBNjZDNDIiLz48dGV4dCB4PSI1MCUiIHk9IjU1JSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIxMCIgZmlsbD0id2hpdGUiIGZvbnQtd2VpZ2h0PSJib2xkIj5PPC90ZXh0Pjwvc3ZnPg=='">
                        <span style="font-weight: bold; color: var(--primary-brown);">أوكتا</span>
                    </div>
                    
                    <div style="display: flex; gap: 0.5rem; align-items: center;">
                        <button class="control-btn" style="padding: 0.5rem; font-size: 0.8rem;">
                            <i class="fas fa-globe"></i>
                        </button>
                        <button class="control-btn" style="padding: 0.5rem; font-size: 0.8rem;">
                            <i class="fas fa-moon"></i>
                        </button>
                        <button class="mobile-menu-btn">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
                <p style="text-align: center; color: var(--deep-green); margin-top: 1rem; font-weight: bold;">
                    ✅ نظيف ومرتب بدون ازدحام
                </p>
            </div>
        </div>

        <!-- Alternative Solutions -->
        <div class="test-section">
            <h2 class="test-title">💡 بدائل للحجز</h2>
            <div style="background: var(--light-gray); padding: 2rem; border-radius: 10px;">
                <h3 style="color: var(--primary-brown); margin-bottom: 1rem;">طرق أخرى للحجز:</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="background: var(--white); padding: 1rem; border-radius: 8px; text-align: center;">
                        <i class="fas fa-phone" style="color: var(--primary-brown); font-size: 2rem; margin-bottom: 0.5rem;"></i>
                        <h4>الزر العائم</h4>
                        <p style="font-size: 0.9rem;">في الهواتف المحمولة</p>
                    </div>
                    <div style="background: var(--white); padding: 1rem; border-radius: 8px; text-align: center;">
                        <i class="fas fa-envelope" style="color: var(--deep-green); font-size: 2rem; margin-bottom: 0.5rem;"></i>
                        <h4>صفحة الاتصال</h4>
                        <p style="font-size: 0.9rem;">نموذج تواصل كامل</p>
                    </div>
                    <div style="background: var(--white); padding: 1rem; border-radius: 8px; text-align: center;">
                        <i class="fas fa-heart" style="color: var(--accent-gold); font-size: 2rem; margin-bottom: 0.5rem;"></i>
                        <h4>Hero Section</h4>
                        <p style="font-size: 0.9rem;">أزرار الحث على العمل</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Final Status -->
        <div class="test-section" style="background: linear-gradient(135deg, var(--deep-green), var(--primary-brown)); color: white;">
            <div style="text-align: center;">
                <i class="fas fa-check-circle" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                <h2 style="margin-bottom: 1rem;">تم حل المشكلة نهائياً!</h2>
                <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem;">
                    النافبار الآن نظيف ومتناسق بدون أي مشاكل في النص أو التصميم
                </p>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0;">
                    <div>
                        <h4>✅ لا نص مقطوع</h4>
                        <p>في النسخة العربية</p>
                    </div>
                    <div>
                        <h4>✅ لا خروج عن الصفحة</h4>
                        <p>في النسخة الإنجليزية</p>
                    </div>
                    <div>
                        <h4>✅ تصميم نظيف</h4>
                        <p>مرتب ومتناسق</p>
                    </div>
                    <div>
                        <h4>✅ تجاوب مثالي</h4>
                        <p>على جميع الأجهزة</p>
                    </div>
                </div>
                
                <div style="margin-top: 2rem;">
                    <a href="index.html" class="btn" style="background: white; color: var(--primary-brown); margin: 0.5rem;">
                        <i class="fas fa-home"></i>
                        <span>اختبر الموقع العربي</span>
                    </a>
                    <a href="index-en.html" class="btn" style="background: white; color: var(--primary-brown); margin: 0.5rem;">
                        <i class="fas fa-globe"></i>
                        <span>اختبر الموقع الإنجليزي</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم حذف زر "احجز الآن" من النافبار:');
            console.log('✅ النسخة العربية: لا توجد مشاكل في النص');
            console.log('✅ النسخة الإنجليزية: لا يخرج عن الصفحة');
            console.log('✅ التصميم: نظيف ومتناسق');
            console.log('✅ التجاوب: يعمل على جميع الأجهزة');
        });
    </script>
</body>
</html>
