# دليل المطور - موقع أوكتا مطعم وكافيه

## 🚀 البدء السريع

### متطلبات التشغيل
- متصفح حديث يدعم ES6+
- خادم HTTP محلي (اختياري للتطوير)
- محرر نصوص أو IDE

### تشغيل الموقع
```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx http-server

# باستخدام PHP
php -S localhost:8000
```

ثم افتح المتصفح على `http://localhost:8000`

## 📁 هيكل المشروع

```
octa-website/
├── index.html              # الصفحة الرئيسية
├── css/
│   └── styles.css         # ملف التنسيق الرئيسي
├── js/
│   └── script.js          # ملف JavaScript الرئيسي
├── images/                # مجلد الصور
│   ├── logo.png          # شعار أوكتا
│   └── gallery/          # صور المعرض
├── Menu.pdf              # قائمة الطعام الأصلية
├── README.md             # وثائق المشروع
├── IMPROVEMENTS_REPORT.md # تقرير التحسينات
└── DEVELOPER_GUIDE.md    # هذا الملف
```

## 🎨 نظام الألوان

### المتغيرات الأساسية
```css
:root {
    --primary-brown: #A66C42;      /* البني الدافئ - أساسي */
    --secondary-brown: #A4693E;    /* البني الفاتح - مساعد */
    --tertiary-brown: #A56A40;     /* البني الفاتح - مساعد */
    --deep-green: #216354;         /* الأخضر الداكن - ثانوي */
    --white: #FFFFFF;              /* الأبيض - خلفيات */
    --black: #2C2C2C;              /* الأسود - نصوص */
}
```

### استخدام الألوان
- **العناوين**: `var(--primary-brown)`
- **الأزرار الأساسية**: `var(--primary-brown)` إلى `var(--deep-green)`
- **العناصر التفاعلية**: `var(--deep-green)`
- **الأسعار**: `var(--deep-green)` مع خلفية فاتحة

## 🔤 نظام الخطوط

### الخطوط المستخدمة
```css
--font-headings: 'Playfair Display', 'Lora', serif;
--font-body: 'Open Sans', 'Cairo', sans-serif;
--font-arabic: 'Cairo', 'Open Sans', sans-serif;
```

### التسلسل الهرمي
- **H1**: 3rem، bold
- **H2**: 2.5rem، semibold
- **H3**: 2rem، semibold
- **H4**: 1.5rem، medium
- **Body**: 1rem، normal

## 📱 نقاط التوقف (Breakpoints)

```css
/* الهواتف الصغيرة */
@media (max-width: 480px) { }

/* الهواتف والأجهزة اللوحية الصغيرة */
@media (max-width: 768px) { }

/* الأجهزة اللوحية */
@media (max-width: 1024px) { }

/* أجهزة سطح المكتب */
@media (min-width: 1025px) { }
```

## ⚡ الوظائف الرئيسية

### 1. التنقل المحمول
```javascript
// تفعيل/إلغاء القائمة المحمولة
const mobileToggle = document.getElementById('nav-toggle');
const navMenu = document.querySelector('.nav-menu');

mobileToggle.addEventListener('click', function() {
    this.classList.toggle('active');
    navMenu.classList.toggle('active');
    document.body.classList.toggle('menu-open');
});
```

### 2. تبديل تبويبات القائمة
```javascript
// تبديل تبويبات قائمة الطعام
const tabButtons = document.querySelectorAll('.tab-btn');
tabButtons.forEach(button => {
    button.addEventListener('click', function() {
        const targetTab = this.getAttribute('data-tab');
        // إزالة الفئة النشطة من جميع التبويبات
        // إضافة الفئة النشطة للتبويب المحدد
    });
});
```

### 3. البحث والفلترة
```javascript
// بحث في قائمة الطعام
function filterMenuItems(searchTerm, priceRange) {
    const items = document.querySelectorAll('.menu-item');
    items.forEach(item => {
        // منطق البحث والفلترة
    });
}
```

### 4. الصور البديلة
```javascript
// إنشاء صور placeholder للصور المفقودة
function generatePlaceholderImage(text, width, height) {
    const svg = `<svg>...</svg>`;
    return 'data:image/svg+xml;base64,' + btoa(svg);
}
```

## 🔧 إضافة محتوى جديد

### إضافة عنصر قائمة جديد
```html
<div class="menu-item">
    <img src="images/new-dish.jpg" alt="وصف الطبق" loading="lazy">
    <div class="menu-item-content">
        <h3>اسم الطبق</h3>
        <p>وصف الطبق...</p>
        <span class="price">السعر جنيه</span>
    </div>
</div>
```

### إضافة فرع جديد
```html
<div class="branch-card">
    <div class="branch-header">
        <h3>اسم الفرع</h3>
        <span class="branch-city">المدينة</span>
    </div>
    <div class="branch-info">
        <div class="info-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>العنوان</span>
        </div>
        <!-- معلومات إضافية -->
    </div>
    <button class="btn btn-primary view-map" data-location="location-id">
        عرض على الخريطة
    </button>
</div>
```

## 🎯 أفضل الممارسات

### الأداء
1. **استخدم lazy loading للصور**
2. **قلل من استخدام JavaScript الثقيل**
3. **استخدم CSS Variables للألوان**
4. **اضغط الصور قبل الرفع**

### إمكانية الوصول
1. **أضف alt text لجميع الصور**
2. **استخدم ARIA labels للعناصر التفاعلية**
3. **تأكد من التباين اللوني الكافي**
4. **اختبر التنقل بلوحة المفاتيح**

### الصيانة
1. **اختبر الموقع بانتظام**
2. **حدث المحتوى دورياً**
3. **راقب الأداء**
4. **احتفظ بنسخ احتياطية**

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### القائمة المحمولة لا تعمل
```javascript
// تأكد من وجود العناصر
const mobileToggle = document.getElementById('nav-toggle');
if (!mobileToggle) {
    console.error('Mobile toggle button not found');
}
```

#### الصور لا تظهر
```javascript
// تحقق من مسار الصور
img.onerror = function() {
    this.src = generatePlaceholderImage(this.alt);
};
```

#### مشاكل التنسيق
```css
/* تأكد من تحميل CSS Variables */
:root {
    --primary-brown: #A66C42;
    /* باقي المتغيرات */
}
```

## 📞 الدعم والمساعدة

### الموارد المفيدة
- [MDN Web Docs](https://developer.mozilla.org/)
- [CSS-Tricks](https://css-tricks.com/)
- [Can I Use](https://caniuse.com/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

### نصائح للتطوير
1. **استخدم أدوات المطور في المتصفح**
2. **اختبر على أجهزة مختلفة**
3. **استخدم Git للتحكم في الإصدارات**
4. **وثق التغييرات التي تقوم بها**

---

**آخر تحديث**: 20 يوليو 2025  
**الإصدار**: 2.0  
**المطور**: Augment Agent
