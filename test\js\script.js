/**
 * OCTA RESTAURANT - ADVANCED JAVASCRIPT
 * Modern, interactive, and performance-optimized
 * Created with love for Italian cuisine experience
 */

// ===================================
// GLOBAL VARIABLES AND CONFIGURATION
// ===================================

const CONFIG = {
    animationDuration: 300,
    scrollOffset: 80,
    debounceDelay: 250,
    particleCount: 50,
    menuData: null,
    galleryData: null
};

// State management
const STATE = {
    isMenuOpen: false,
    isDarkMode: false,
    currentGalleryIndex: 0,
    isLoading: true,
    activeSection: 'home'
};

// ===================================
// UTILITY FUNCTIONS
// ===================================

// Debounce function for performance optimization
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Throttle function for scroll events
const throttle = (func, limit) => {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
};

// Smooth scroll to element
const smoothScrollTo = (target, offset = CONFIG.scrollOffset) => {
    const element = document.querySelector(target);
    if (element) {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;
        
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
};

// Generate placeholder image
const generatePlaceholderImage = (text, width = 400, height = 300) => {
    const svg = `
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#A66C42;stop-opacity:0.2" />
                    <stop offset="100%" style="stop-color:#216354;stop-opacity:0.2" />
                </linearGradient>
            </defs>
            <rect width="100%" height="100%" fill="url(#grad)" stroke="#A66C42" stroke-width="2" stroke-dasharray="10,5"/>
            <text x="50%" y="45%" text-anchor="middle" font-family="Cairo, Arial, sans-serif" font-size="18" fill="#A66C42" font-weight="600">
                ${text}
            </text>
            <text x="50%" y="60%" text-anchor="middle" font-family="Cairo, Arial, sans-serif" font-size="14" fill="#666" opacity="0.8">
                قيد التحميل...
            </text>
            <circle cx="50%" cy="75%" r="15" fill="none" stroke="#A66C42" stroke-width="2" opacity="0.6">
                <animate attributeName="stroke-dasharray" values="0 94;47 47;0 94" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="stroke-dashoffset" values="0;-47;-94" dur="2s" repeatCount="indefinite"/>
            </circle>
        </svg>
    `;
    
    return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
};

// Intersection Observer for animations
const createObserver = (callback, options = {}) => {
    const defaultOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };
    
    return new IntersectionObserver(callback, { ...defaultOptions, ...options });
};

// ===================================
// LOADING SCREEN
// ===================================

class LoadingScreen {
    constructor() {
        this.loadingElement = document.getElementById('loading-screen');
        this.init();
    }
    
    init() {
        // Simulate loading time
        setTimeout(() => {
            this.hide();
        }, 2000);
    }
    
    hide() {
        if (this.loadingElement) {
            this.loadingElement.classList.add('hidden');
            setTimeout(() => {
                this.loadingElement.style.display = 'none';
                STATE.isLoading = false;
                document.body.classList.remove('loading');
            }, 500);
        }
    }
}

// ===================================
// NAVIGATION SYSTEM
// ===================================

class Navigation {
    constructor() {
        this.navbar = document.getElementById('navbar');
        this.navToggle = document.getElementById('nav-toggle');
        this.navMenu = document.querySelector('.nav-menu');
        this.navLinks = document.querySelectorAll('.nav-link');
        this.themeToggle = document.getElementById('theme-toggle');
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.handleScroll();
        this.initTheme();
    }
    
    bindEvents() {
        // Mobile menu toggle
        if (this.navToggle) {
            this.navToggle.addEventListener('click', () => this.toggleMobileMenu());
        }
        
        // Navigation links
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleNavClick(e));
        });
        
        // Theme toggle
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // Scroll events
        window.addEventListener('scroll', throttle(() => this.handleScroll(), 100));
        
        // Close mobile menu on outside click
        document.addEventListener('click', (e) => {
            if (!this.navbar.contains(e.target) && STATE.isMenuOpen) {
                this.closeMobileMenu();
            }
        });
        
        // Close mobile menu on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && STATE.isMenuOpen) {
                this.closeMobileMenu();
            }
        });

        // Handle window resize
        window.addEventListener('resize', debounce(() => this.handleResize(), 250));
    }

    handleResize() {
        // Close mobile menu on desktop
        if (window.innerWidth > 768 && STATE.isMenuOpen) {
            this.closeMobileMenu();
        }

        // Update viewport height for mobile
        if (window.innerWidth <= 768) {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
    }
    
    toggleMobileMenu() {
        STATE.isMenuOpen = !STATE.isMenuOpen;

        this.navToggle.classList.toggle('active');
        this.navMenu.classList.toggle('active');
        document.body.classList.toggle('menu-open');

        // Update ARIA attributes
        this.navToggle.setAttribute('aria-expanded', STATE.isMenuOpen);
        this.navToggle.setAttribute('aria-label', STATE.isMenuOpen ? 'إغلاق القائمة' : 'فتح القائمة');

        // Add smooth animation for mobile menu
        if (STATE.isMenuOpen) {
            // Prevent scrolling when menu is open
            document.body.style.overflow = 'hidden';
            document.body.style.position = 'fixed';
            document.body.style.width = '100%';
            document.body.style.top = `-${window.scrollY}px`;
        } else {
            // Restore scrolling
            const scrollY = document.body.style.top;
            document.body.style.overflow = '';
            document.body.style.position = '';
            document.body.style.width = '';
            document.body.style.top = '';
            window.scrollTo(0, parseInt(scrollY || '0') * -1);
        }
    }
    
    closeMobileMenu() {
        STATE.isMenuOpen = false;
        this.navToggle.classList.remove('active');
        this.navMenu.classList.remove('active');
        document.body.classList.remove('menu-open');

        // Restore scrolling
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.width = '';
        document.body.style.top = '';

        this.navToggle.setAttribute('aria-expanded', 'false');
        this.navToggle.setAttribute('aria-label', 'فتح القائمة');
    }
    
    handleNavClick(e) {
        e.preventDefault();
        const target = e.currentTarget.getAttribute('href');
        const section = e.currentTarget.getAttribute('data-section');
        
        if (target && target.startsWith('#')) {
            smoothScrollTo(target);
            this.setActiveLink(section);
            
            // Close mobile menu if open
            if (STATE.isMenuOpen) {
                this.closeMobileMenu();
            }
        }
    }
    
    setActiveLink(section) {
        this.navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-section') === section) {
                link.classList.add('active');
            }
        });
        STATE.activeSection = section;
    }
    
    handleScroll() {
        const scrollY = window.scrollY;
        
        // Add scrolled class to navbar
        if (scrollY > 50) {
            this.navbar.classList.add('scrolled');
        } else {
            this.navbar.classList.remove('scrolled');
        }
        
        // Update active section based on scroll position
        this.updateActiveSection();
    }
    
    updateActiveSection() {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + CONFIG.scrollOffset + 100;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                this.setActiveLink(sectionId);
            }
        });
    }
    
    initTheme() {
        // Check for saved theme preference or system preference
        const savedTheme = localStorage.getItem('octa-theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const defaultTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

        this.setTheme(defaultTheme);

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('octa-theme')) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);

        // Add visual feedback
        this.themeToggle.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.themeToggle.style.transform = 'scale(1)';
        }, 150);
    }

    setTheme(theme) {
        // Ensure theme is valid
        if (theme !== 'light' && theme !== 'dark') {
            theme = 'light';
        }

        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('octa-theme', theme);
        STATE.isDarkMode = theme === 'dark';

        // Update theme toggle icon with animation
        const themeIcon = document.getElementById('theme-icon');
        if (themeIcon) {
            themeIcon.style.transform = 'rotate(180deg)';
            setTimeout(() => {
                themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                themeIcon.style.transform = 'rotate(0deg)';
            }, 150);
        }

        // Update theme toggle aria-label
        if (this.themeToggle) {
            this.themeToggle.setAttribute('aria-label',
                theme === 'dark' ? 'تبديل إلى الوضع الفاتح' : 'تبديل إلى الوضع المظلم'
            );
        }

        // Force update mobile menu theme if it's open
        if (STATE.isMenuOpen && this.navMenu) {
            this.navMenu.style.background = theme === 'dark'
                ? 'rgba(26, 26, 26, 0.98)'
                : 'rgba(255, 255, 255, 0.98)';
        }

        // Dispatch custom event for theme change
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme, isDark: theme === 'dark' }
        }));
    }
}

// ===================================
// HERO SECTION ANIMATIONS
// ===================================

class HeroAnimations {
    constructor() {
        this.heroSection = document.querySelector('.hero');
        this.particlesContainer = document.getElementById('hero-particles');
        this.init();
    }
    
    init() {
        this.createParticles();
        this.animateHeroElements();
    }
    
    createParticles() {
        if (!this.particlesContainer) return;
        
        // Create animated particles for hero background
        for (let i = 0; i < CONFIG.particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 4 + 2}px;
                height: ${Math.random() * 4 + 2}px;
                background: ${Math.random() > 0.5 ? '#A66C42' : '#216354'};
                border-radius: 50%;
                opacity: ${Math.random() * 0.5 + 0.2};
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
                animation-delay: ${Math.random() * 2}s;
            `;
            this.particlesContainer.appendChild(particle);
        }
    }
    
    animateHeroElements() {
        const heroText = document.querySelector('.hero-text');
        const heroImage = document.querySelector('.hero-image');
        
        if (heroText) {
            setTimeout(() => {
                heroText.style.opacity = '1';
                heroText.style.transform = 'translateX(0)';
            }, 500);
        }
        
        if (heroImage) {
            setTimeout(() => {
                heroImage.style.opacity = '1';
                heroImage.style.transform = 'translateX(0)';
            }, 800);
        }
    }
}

// ===================================
// SCROLL ANIMATIONS
// ===================================

class ScrollAnimations {
    constructor() {
        this.init();
    }
    
    init() {
        this.observeElements();
        this.observeCounters();
    }
    
    observeElements() {
        const animatedElements = document.querySelectorAll('.fade-in, .fade-in-left, .fade-in-right');
        
        const observer = createObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) translateX(0)';
                }
            });
        });
        
        animatedElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });
    }
    
    observeCounters() {
        const counters = document.querySelectorAll('[data-count]');
        
        const counterObserver = createObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        });
        
        counters.forEach(counter => counterObserver.observe(counter));
    }
    
    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-count'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString();
        }, 16);
    }
}

// ===================================
// MENU SYSTEM
// ===================================

class MenuSystem {
    constructor() {
        this.menuGrid = document.getElementById('menu-grid');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.searchInput = document.getElementById('menu-search');
        this.menuItems = [];
        this.filteredItems = [];
        this.currentFilter = 'all';

        this.init();
    }

    init() {
        this.loadMenuData();
        this.bindEvents();
    }

    loadMenuData() {
        // Sample menu data - in real app, this would come from API
        this.menuItems = [
            {
                id: 1,
                name: 'بيتزا مارغريتا',
                description: 'بيتزا إيطالية كلاسيكية مع الطماطم الطازجة والموزاريلا والريحان',
                price: '120',
                category: 'lunch',
                image: '../images/pizza-margherita.jpg',
                featured: true
            },
            {
                id: 2,
                name: 'باستا كاربونارا',
                description: 'معكرونة إيطالية كريمية مع البيكون والبيض والجبن البارميزان',
                price: '95',
                category: 'lunch',
                image: '../images/pasta-carbonara.jpg',
                featured: false
            },
            {
                id: 3,
                name: 'كابتشينو إيطالي',
                description: 'قهوة إيطالية أصيلة مع رغوة الحليب المثالية',
                price: '35',
                category: 'drinks',
                image: '../images/cappuccino.jpg',
                featured: true
            },
            {
                id: 4,
                name: 'تيراميسو',
                description: 'حلوى إيطالية كلاسيكية مع القهوة والماسكاربوني',
                price: '65',
                category: 'desserts',
                image: '../images/tiramisu.jpg',
                featured: true
            },
            {
                id: 5,
                name: 'فطور إيطالي',
                description: 'فطور إيطالي تقليدي مع الكرواسان والقهوة',
                price: '85',
                category: 'breakfast',
                image: '../images/italian-breakfast.jpg',
                featured: false
            },
            {
                id: 6,
                name: 'ستيك فلورنتين',
                description: 'ستيك إيطالي فاخر مشوي على الفحم مع الأعشاب',
                price: '250',
                category: 'dinner',
                image: '../images/steak-florentine.jpg',
                featured: true
            }
        ];

        this.filteredItems = [...this.menuItems];
        this.renderMenuItems();
    }

    bindEvents() {
        // Filter buttons
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleFilter(e));
        });

        // Search input
        if (this.searchInput) {
            this.searchInput.addEventListener('input', debounce((e) => this.handleSearch(e), CONFIG.debounceDelay));
        }
    }

    handleFilter(e) {
        const filter = e.currentTarget.getAttribute('data-filter');
        this.currentFilter = filter;

        // Update active filter button
        this.filterButtons.forEach(btn => btn.classList.remove('active'));
        e.currentTarget.classList.add('active');

        this.applyFilters();
    }

    handleSearch(e) {
        const searchTerm = e.target.value.toLowerCase().trim();
        this.searchTerm = searchTerm;
        this.applyFilters();
    }

    applyFilters() {
        let filtered = [...this.menuItems];

        // Apply category filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(item => item.category === this.currentFilter);
        }

        // Apply search filter
        if (this.searchTerm) {
            filtered = filtered.filter(item =>
                item.name.toLowerCase().includes(this.searchTerm) ||
                item.description.toLowerCase().includes(this.searchTerm)
            );
        }

        this.filteredItems = filtered;
        this.renderMenuItems();
    }

    renderMenuItems() {
        if (!this.menuGrid) return;

        // Clear existing items
        this.menuGrid.innerHTML = '';

        if (this.filteredItems.length === 0) {
            this.menuGrid.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد نتائج</h3>
                    <p>جرب البحث بكلمات أخرى أو اختر فئة مختلفة</p>
                </div>
            `;
            return;
        }

        this.filteredItems.forEach((item, index) => {
            const menuItemElement = this.createMenuItemElement(item);
            this.menuGrid.appendChild(menuItemElement);

            // Animate item appearance
            setTimeout(() => {
                menuItemElement.classList.add('show');
            }, index * 100);
        });
    }

    createMenuItemElement(item) {
        const menuItem = document.createElement('div');
        menuItem.className = 'menu-item';
        menuItem.setAttribute('data-category', item.category);

        menuItem.innerHTML = `
            <div class="menu-item-image">
                <img src="${item.image}" alt="${item.name}" loading="lazy" onerror="this.src='${generatePlaceholderImage(item.name, 350, 200)}'">
                ${item.featured ? '<div class="featured-badge"><i class="fas fa-star"></i></div>' : ''}
            </div>
            <div class="menu-item-content">
                <h3>${item.name}</h3>
                <p>${item.description}</p>
                <div class="menu-item-footer">
                    <span class="menu-price">${item.price} جنيه</span>
                    <button class="btn btn-primary btn-sm add-to-cart" data-id="${item.id}">
                        <i class="fas fa-plus"></i>
                        <span>إضافة</span>
                    </button>
                </div>
            </div>
        `;

        return menuItem;
    }
}

// ===================================
// GALLERY SYSTEM
// ===================================

class GallerySystem {
    constructor() {
        this.galleryGrid = document.getElementById('gallery-grid');
        this.galleryFilter = document.querySelector('.gallery-filter');
        this.lightbox = document.getElementById('lightbox');
        this.galleryItems = [];
        this.filteredGallery = [];
        this.currentFilter = 'all';

        this.init();
    }

    init() {
        this.loadGalleryData();
        this.bindEvents();
        this.initLightbox();
    }

    loadGalleryData() {
        // Sample gallery data
        this.galleryItems = [
            {
                id: 1,
                src: '../images/gallery/food-1.jpg',
                alt: 'بيتزا إيطالية شهية',
                category: 'food',
                caption: 'بيتزا مارغريتا الإيطالية الأصيلة'
            },
            {
                id: 2,
                src: '../images/gallery/interior-1.jpg',
                alt: 'ديكور المطعم الأنيق',
                category: 'interior',
                caption: 'أجواء أوكتا الدافئة والأنيقة'
            },
            {
                id: 3,
                src: '../images/gallery/food-2.jpg',
                alt: 'باستا إيطالية',
                category: 'food',
                caption: 'باستا كاربونارا الكريمية'
            },
            {
                id: 4,
                src: '../images/gallery/events-1.jpg',
                alt: 'فعالية في المطعم',
                category: 'events',
                caption: 'أمسية موسيقية في أوكتا'
            },
            {
                id: 5,
                src: '../images/gallery/food-3.jpg',
                alt: 'حلوى تيراميسو',
                category: 'food',
                caption: 'تيراميسو إيطالي أصيل'
            },
            {
                id: 6,
                src: '../images/gallery/interior-2.jpg',
                alt: 'منطقة الجلوس',
                category: 'interior',
                caption: 'منطقة جلوس مريحة وأنيقة'
            }
        ];

        this.filteredGallery = [...this.galleryItems];
        this.renderGalleryItems();
    }

    bindEvents() {
        // Gallery filter buttons
        if (this.galleryFilter) {
            const filterButtons = this.galleryFilter.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', (e) => this.handleGalleryFilter(e));
            });
        }
    }

    handleGalleryFilter(e) {
        const filter = e.currentTarget.getAttribute('data-filter');
        this.currentFilter = filter;

        // Update active filter button
        const filterButtons = this.galleryFilter.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => btn.classList.remove('active'));
        e.currentTarget.classList.add('active');

        this.applyGalleryFilter();
    }

    applyGalleryFilter() {
        if (this.currentFilter === 'all') {
            this.filteredGallery = [...this.galleryItems];
        } else {
            this.filteredGallery = this.galleryItems.filter(item => item.category === this.currentFilter);
        }

        this.renderGalleryItems();
    }

    renderGalleryItems() {
        if (!this.galleryGrid) return;

        this.galleryGrid.innerHTML = '';

        this.filteredGallery.forEach((item, index) => {
            const galleryItem = this.createGalleryItemElement(item, index);
            this.galleryGrid.appendChild(galleryItem);

            // Animate item appearance
            setTimeout(() => {
                galleryItem.classList.add('show');
            }, index * 100);
        });
    }

    createGalleryItemElement(item, index) {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item';
        galleryItem.setAttribute('data-category', item.category);
        galleryItem.setAttribute('data-index', index);

        galleryItem.innerHTML = `
            <img src="${item.src}" alt="${item.alt}" loading="lazy" onerror="this.src='${generatePlaceholderImage(item.alt, 300, 300)}'">
            <div class="gallery-overlay">
                <i class="fas fa-expand"></i>
            </div>
        `;

        // Add click event for lightbox
        galleryItem.addEventListener('click', () => this.openLightbox(index));

        return galleryItem;
    }

    initLightbox() {
        if (!this.lightbox) return;

        const lightboxClose = this.lightbox.querySelector('.lightbox-close');
        const lightboxPrev = this.lightbox.querySelector('.lightbox-prev');
        const lightboxNext = this.lightbox.querySelector('.lightbox-next');

        // Close lightbox
        lightboxClose?.addEventListener('click', () => this.closeLightbox());

        // Navigation
        lightboxPrev?.addEventListener('click', () => this.prevImage());
        lightboxNext?.addEventListener('click', () => this.nextImage());

        // Close on background click
        this.lightbox.addEventListener('click', (e) => {
            if (e.target === this.lightbox) {
                this.closeLightbox();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (this.lightbox.classList.contains('active')) {
                switch (e.key) {
                    case 'Escape':
                        this.closeLightbox();
                        break;
                    case 'ArrowLeft':
                        this.nextImage();
                        break;
                    case 'ArrowRight':
                        this.prevImage();
                        break;
                }
            }
        });
    }

    openLightbox(index) {
        STATE.currentGalleryIndex = index;
        this.updateLightboxImage();
        this.lightbox.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeLightbox() {
        this.lightbox.classList.remove('active');
        document.body.style.overflow = '';
    }

    prevImage() {
        STATE.currentGalleryIndex = STATE.currentGalleryIndex > 0
            ? STATE.currentGalleryIndex - 1
            : this.filteredGallery.length - 1;
        this.updateLightboxImage();
    }

    nextImage() {
        STATE.currentGalleryIndex = STATE.currentGalleryIndex < this.filteredGallery.length - 1
            ? STATE.currentGalleryIndex + 1
            : 0;
        this.updateLightboxImage();
    }

    updateLightboxImage() {
        const currentItem = this.filteredGallery[STATE.currentGalleryIndex];
        const lightboxImage = this.lightbox.querySelector('.lightbox-image');
        const lightboxCaption = this.lightbox.querySelector('.lightbox-caption');

        if (lightboxImage && currentItem) {
            lightboxImage.src = currentItem.src;
            lightboxImage.alt = currentItem.alt;
        }

        if (lightboxCaption && currentItem.caption) {
            lightboxCaption.textContent = currentItem.caption;
        }
    }
}

// ===================================
// FORM HANDLING
// ===================================

class FormHandler {
    constructor() {
        this.contactForm = document.getElementById('contact-form');
        this.newsletterForm = document.querySelector('.newsletter-form');
        this.init();
    }

    init() {
        this.bindEvents();
        this.initFormValidation();
    }

    bindEvents() {
        // Contact form
        if (this.contactForm) {
            this.contactForm.addEventListener('submit', (e) => this.handleContactSubmit(e));
        }

        // Newsletter form
        if (this.newsletterForm) {
            this.newsletterForm.addEventListener('submit', (e) => this.handleNewsletterSubmit(e));
        }

        // Form inputs animation
        const formInputs = document.querySelectorAll('.form-input');
        formInputs.forEach(input => {
            input.addEventListener('focus', () => this.handleInputFocus(input));
            input.addEventListener('blur', () => this.handleInputBlur(input));
        });
    }

    initFormValidation() {
        const inputs = document.querySelectorAll('.form-input[required]');
        inputs.forEach(input => {
            input.addEventListener('input', () => this.validateInput(input));
        });
    }

    handleContactSubmit(e) {
        e.preventDefault();

        const formData = new FormData(this.contactForm);
        // const data = Object.fromEntries(formData); // For future API integration

        // Validate form
        if (!this.validateForm(this.contactForm)) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // Show loading state
        const submitBtn = this.contactForm.querySelector('.form-submit');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري الإرسال...</span>';
        submitBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
            this.showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً', 'success');
            this.contactForm.reset();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }

    handleNewsletterSubmit(e) {
        e.preventDefault();

        const emailInput = this.newsletterForm.querySelector('.newsletter-input');
        const email = emailInput.value.trim();

        if (!this.isValidEmail(email)) {
            this.showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
            return;
        }

        // Show success message
        this.showNotification('تم الاشتراك بنجاح في النشرة الإخبارية!', 'success');
        emailInput.value = '';
    }

    validateForm(form) {
        const requiredInputs = form.querySelectorAll('[required]');
        let isValid = true;

        requiredInputs.forEach(input => {
            if (!this.validateInput(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateInput(input) {
        const value = input.value.trim();
        const type = input.type;
        let isValid = true;

        // Remove previous error styling
        input.classList.remove('error');

        // Check if required field is empty
        if (input.hasAttribute('required') && !value) {
            isValid = false;
        }

        // Email validation
        if (type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
        }

        // Phone validation (basic)
        if (type === 'tel' && value && !/^[\d\s\-\+\(\)]+$/.test(value)) {
            isValid = false;
        }

        // Add error styling if invalid
        if (!isValid) {
            input.classList.add('error');
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    handleInputFocus(input) {
        input.parentElement.classList.add('focused');
    }

    handleInputBlur(input) {
        if (!input.value.trim()) {
            input.parentElement.classList.remove('focused');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" aria-label="إغلاق">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);

        // Auto hide after 5 seconds
        setTimeout(() => this.hideNotification(notification), 5000);

        // Close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });
    }

    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }
        }, 300);
    }
}

// ===================================
// PERFORMANCE OPTIMIZATIONS
// ===================================

class PerformanceOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.lazyLoadImages();
        this.preloadCriticalResources();
        this.optimizeScrollPerformance();
    }

    lazyLoadImages() {
        const images = document.querySelectorAll('img[loading="lazy"]');

        if ('IntersectionObserver' in window) {
            const imageObserver = createObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;

                        // Handle image load error
                        img.addEventListener('error', function() {
                            this.src = generatePlaceholderImage(this.alt || 'صورة', 400, 300);
                            this.classList.add('placeholder-image');
                        });

                        // Handle successful load
                        img.addEventListener('load', function() {
                            this.classList.add('loaded');
                        });

                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for browsers without IntersectionObserver
            images.forEach(img => {
                img.src = img.src;
            });
        }
    }

    preloadCriticalResources() {
        // Preload critical images
        const criticalImages = [
            '../images/logo.png',
            '../images/hero-dish.jpg'
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }

    optimizeScrollPerformance() {
        // Use passive event listeners for better scroll performance
        let ticking = false;

        const updateScrollElements = () => {
            // Update scroll-dependent elements here
            ticking = false;
        };

        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollElements);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestScrollUpdate, { passive: true });
    }
}

// ===================================
// APPLICATION INITIALIZATION
// ===================================

class OctaApp {
    constructor() {
        this.components = {};
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        try {
            // Initialize all components
            this.components.loadingScreen = new LoadingScreen();
            this.components.navigation = new Navigation();
            this.components.heroAnimations = new HeroAnimations();
            this.components.scrollAnimations = new ScrollAnimations();
            this.components.menuSystem = new MenuSystem();
            this.components.gallerySystem = new GallerySystem();
            this.components.formHandler = new FormHandler();
            this.components.performanceOptimizer = new PerformanceOptimizer();

            // Initialize smooth scrolling for CTA buttons
            this.initSmoothScrolling();

            // Initialize branch interactions
            this.initBranchInteractions();

            console.log('🍕 Octa Restaurant App initialized successfully!');

        } catch (error) {
            console.error('Error initializing Octa App:', error);
        }
    }

    initSmoothScrolling() {
        const scrollButtons = document.querySelectorAll('[data-scroll]');
        scrollButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const target = button.getAttribute('data-scroll');
                smoothScrollTo(`#${target}`);
            });
        });
    }

    initBranchInteractions() {
        // Direction buttons
        const directionButtons = document.querySelectorAll('[data-location]');
        directionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const location = button.getAttribute('data-location');
                this.openDirections(location);
            });
        });

        // Reservation buttons
        const reservationButtons = document.querySelectorAll('[data-branch]');
        reservationButtons.forEach(button => {
            button.addEventListener('click', () => {
                const branch = button.getAttribute('data-branch');
                this.openReservation(branch);
            });
        });
    }

    openDirections(location) {
        const locations = {
            cairo: 'https://maps.google.com/?q=New+Cairo+Egypt',
            alexandria: 'https://maps.google.com/?q=Sidi+Gaber+Alexandria+Egypt'
        };

        if (locations[location]) {
            window.open(locations[location], '_blank');
        }
    }

    openReservation(branch) {
        // In a real app, this would open a reservation modal or redirect to booking page
        smoothScrollTo('#contact');

        // Focus on the contact form
        setTimeout(() => {
            const nameInput = document.getElementById('name');
            if (nameInput) {
                nameInput.focus();
            }
        }, 500);
    }
}

// ===================================
// INITIALIZE APPLICATION
// ===================================

// Create global app instance
window.OctaApp = new OctaApp();

// Add some CSS for notifications and error states
const additionalStyles = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        padding: 16px 20px;
        display: flex;
        align-items: center;
        gap: 12px;
        z-index: 10000;
        transform: translateX(400px);
        opacity: 0;
        transition: all 0.3s ease;
        max-width: 400px;
    }

    .notification.show {
        transform: translateX(0);
        opacity: 1;
    }

    .notification-success {
        border-left: 4px solid #216354;
    }

    .notification-error {
        border-left: 4px solid #e74c3c;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
    }

    .notification-success i {
        color: #216354;
    }

    .notification-error i {
        color: #e74c3c;
    }

    .notification-close {
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background 0.2s ease;
    }

    .notification-close:hover {
        background: #f0f0f0;
    }

    .form-input.error {
        border-color: #e74c3c;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    }

    .no-results {
        text-align: center;
        padding: 60px 20px;
        color: #666;
        grid-column: 1 / -1;
    }

    .no-results i {
        font-size: 3rem;
        color: #A66C42;
        margin-bottom: 20px;
    }

    .featured-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        background: #216354;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .btn-sm {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .add-to-cart:hover {
        background: linear-gradient(135deg, #216354 0%, #A66C42 100%);
    }

    /* Dark theme for notifications */
    [data-theme="dark"] .notification {
        background: #2A2A2A;
        color: #FFFFFF;
        border-left-color: #A66C42;
    }

    [data-theme="dark"] .notification-close:hover {
        background: #3A3A3A;
    }

    /* Smooth transitions for theme switching */
    html {
        transition: background-color 0.3s ease;
    }

    body,
    .navbar,
    .menu-item,
    .branch-card,
    .contact-form,
    .form-input,
    .search-input,
    .newsletter-input,
    .filter-btn {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
