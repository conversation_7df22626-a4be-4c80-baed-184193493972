<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر احجز الآن - مطعم أوكتا</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            background: var(--warm-cream);
            min-height: 100vh;
        }
        
        .test-section {
            background: var(--white);
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }
        
        .test-title {
            color: var(--primary-brown);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            text-align: center;
        }
        
        .navbar-demo {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(166, 108, 66, 0.1);
            border-radius: 15px;
            padding: 1rem 2rem;
            margin: 2rem 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo-demo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .logo-demo img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
        }
        
        .logo-demo span {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-brown);
        }
        
        .nav-links-demo {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .nav-link-demo {
            padding: 0.5rem 1rem;
            color: var(--black);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .nav-link-demo:hover {
            background: rgba(166, 108, 66, 0.1);
            color: var(--primary-brown);
        }
        
        .button-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .button-demo {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .button-demo h3 {
            color: var(--primary-brown);
            margin-bottom: 1rem;
        }
        
        .button-demo p {
            color: var(--gray);
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        
        .size-comparison {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            margin: 2rem 0;
        }
        
        .size-label {
            background: var(--light-gray);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            min-width: 200px;
        }
        
        .size-label h4 {
            color: var(--primary-brown);
            margin-bottom: 1rem;
        }
        
        .responsive-test {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 10px;
            margin: 2rem 0;
        }
        
        .responsive-test h3 {
            color: var(--primary-brown);
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .device-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .device-frame {
            background: var(--white);
            border: 2px solid var(--elegant-border);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }
        
        .device-frame h4 {
            color: var(--deep-green);
            margin-bottom: 1rem;
            font-size: 1rem;
        }
        
        .device-navbar {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(166, 108, 66, 0.1);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.8rem;
        }
        
        .device-navbar .nav-cta {
            font-size: 0.7rem;
            padding: 0.3rem 0.8rem;
        }
        
        .mobile-frame .nav-cta {
            display: none;
        }
        
        .mobile-frame::after {
            content: "زر احجز الآن مخفي في الهواتف";
            display: block;
            margin-top: 1rem;
            color: var(--gray);
            font-size: 0.8rem;
        }
    </style>
</head>
<body data-lang="ar">
    <div class="test-container">
        <!-- Header -->
        <div class="test-section">
            <h1 class="test-title">🔘 اختبار زر "احجز الآن"</h1>
            <p style="text-align: center; color: var(--gray); font-size: 1.1rem;">
                اختبار شامل لأحجام وتنسيق زر "احجز الآن" في النافبار
            </p>
        </div>

        <!-- Current Navbar Demo -->
        <div class="test-section">
            <h2 class="test-title">📱 النافبار الحالي</h2>
            <div class="navbar-demo">
                <div class="logo-demo">
                    <img src="../images/logo.png" alt="OCTA Logo" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJsb2dvR3JhZCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+PHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I0E2NkM0MiIgLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMyMTYzNTQiIC8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMTgiIGZpbGw9InVybCgjbG9nb0dyYWQpIiBzdHJva2U9IiNBNjZDNDIiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTUlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iUGxheWZhaXIgRGlzcGxheSwgc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IndoaXRlIiBmb250LXdlaWdodD0iYm9sZCI+TzwvdGV4dD48L3N2Zz4='">
                    <span>أوكتا</span>
                </div>
                
                <div class="nav-links-demo">
                    <a href="#" class="nav-link-demo">الرئيسية</a>
                    <a href="#" class="nav-link-demo">عن أوكتا</a>
                    <a href="#" class="nav-link-demo">القائمة</a>
                    <a href="#" class="nav-link-demo">المعرض</a>
                    <a href="#" class="nav-link-demo">الفروع</a>
                    <a href="#" class="nav-link-demo">اتصل بنا</a>
                </div>
                
                <a href="#contact" class="nav-cta cta-btn">
                    <i class="fas fa-phone"></i>
                    <span>احجز الآن</span>
                </a>
            </div>
            <p style="text-align: center; color: var(--deep-green); font-weight: bold;">
                ✅ الزر الآن بحجم مناسب ومتناسق مع النافبار
            </p>
        </div>

        <!-- Button Showcase -->
        <div class="test-section">
            <h2 class="test-title">🎨 عرض أحجام الأزرار</h2>
            <div class="button-showcase">
                <div class="button-demo">
                    <h3>الزر في النافبار</h3>
                    <p>حجم مضغوط ومناسب للنافبار</p>
                    <a href="#" class="nav-cta cta-btn">
                        <i class="fas fa-phone"></i>
                        <span>احجز الآن</span>
                    </a>
                </div>
                
                <div class="button-demo">
                    <h3>الزر العادي</h3>
                    <p>الحجم الافتراضي للأزرار</p>
                    <a href="#" class="cta-btn">
                        <i class="fas fa-calendar-alt"></i>
                        <span>احجز طاولة</span>
                    </a>
                </div>
                
                <div class="button-demo">
                    <h3>زر الهاتف المحمول</h3>
                    <p>الزر العائم في الهواتف</p>
                    <a href="#" class="cta-btn floating" style="position: relative; bottom: auto; right: auto;">
                        <i class="fas fa-phone"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Size Comparison -->
        <div class="test-section">
            <h2 class="test-title">📏 مقارنة الأحجام</h2>
            <div class="size-comparison">
                <div class="size-label">
                    <h4>قبل التحسين</h4>
                    <div style="background: #ffebee; color: #c62828; padding: 0.8rem 1.5rem; border-radius: 25px; font-size: 0.9rem; border: 2px solid #c62828;">
                        <i class="fas fa-phone"></i>
                        <span>احجز الآن</span>
                    </div>
                    <p style="margin-top: 0.5rem; font-size: 0.8rem;">كبير جداً</p>
                </div>
                
                <div class="size-label">
                    <h4>بعد التحسين</h4>
                    <a href="#" class="nav-cta cta-btn">
                        <i class="fas fa-phone"></i>
                        <span>احجز الآن</span>
                    </a>
                    <p style="margin-top: 0.5rem; font-size: 0.8rem;">مناسب ومتناسق</p>
                </div>
            </div>
        </div>

        <!-- Responsive Test -->
        <div class="test-section">
            <h2 class="test-title">📱 اختبار التجاوب</h2>
            <div class="responsive-test">
                <h3>عرض الزر على الأجهزة المختلفة</h3>
                <div class="device-demo">
                    <div class="device-frame">
                        <h4>💻 Desktop (1024px+)</h4>
                        <div class="device-navbar">
                            <span>أوكتا</span>
                            <div style="display: flex; gap: 0.5rem; font-size: 0.7rem;">
                                <span>الرئيسية</span>
                                <span>القائمة</span>
                                <span>اتصل بنا</span>
                            </div>
                            <div class="nav-cta cta-btn" style="font-size: 0.7rem; padding: 0.3rem 0.8rem;">
                                <i class="fas fa-phone"></i>
                                <span>احجز الآن</span>
                            </div>
                        </div>
                        <p style="margin-top: 0.5rem; color: var(--deep-green); font-size: 0.8rem;">✅ ظاهر ومناسب</p>
                    </div>
                    
                    <div class="device-frame">
                        <h4>📱 Tablet (768px-1024px)</h4>
                        <div class="device-navbar">
                            <span>أوكتا</span>
                            <div style="display: flex; gap: 0.3rem; font-size: 0.6rem;">
                                <span>الرئيسية</span>
                                <span>القائمة</span>
                            </div>
                            <div class="nav-cta cta-btn" style="font-size: 0.6rem; padding: 0.2rem 0.6rem;">
                                <i class="fas fa-phone"></i>
                                <span>احجز</span>
                            </div>
                        </div>
                        <p style="margin-top: 0.5rem; color: var(--deep-green); font-size: 0.8rem;">✅ ظاهر ومضغوط</p>
                    </div>
                    
                    <div class="device-frame mobile-frame">
                        <h4>📱 Mobile (< 768px)</h4>
                        <div class="device-navbar">
                            <span>أوكتا</span>
                            <div style="font-size: 0.6rem;">☰</div>
                        </div>
                        <p style="margin-top: 0.5rem; color: var(--warning); font-size: 0.8rem;">⚠️ مخفي (يظهر الزر العائم)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="test-section">
            <h2 class="test-title">⚙️ التفاصيل التقنية</h2>
            <div style="background: var(--light-gray); padding: 2rem; border-radius: 10px;">
                <h3 style="color: var(--primary-brown); margin-bottom: 1rem;">التحسينات المطبقة:</h3>
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                        <strong>الحجم:</strong> تم تقليل padding إلى <code>var(--spacing-xs) var(--spacing-md)</code>
                    </li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                        <strong>الخط:</strong> تم تقليل font-size إلى <code>0.9rem</code> للعربية و <code>0.9rem</code> للإنجليزية
                    </li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                        <strong>الارتفاع:</strong> تم تحديد ارتفاع ثابت <code>height: 40px</code>
                    </li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                        <strong>التجاوب:</strong> مخفي في الهواتف، ظاهر في الأجهزة الكبيرة
                    </li>
                    <li style="padding: 0.5rem 0;">
                        <strong>التنسيق:</strong> محاذاة مثالية مع عناصر النافبار الأخرى
                    </li>
                </ul>
            </div>
        </div>

        <!-- Final Status -->
        <div class="test-section" style="background: linear-gradient(135deg, var(--deep-green), var(--primary-brown)); color: white;">
            <div style="text-align: center;">
                <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <h2 style="margin-bottom: 1rem;">تم إصلاح زر "احجز الآن"!</h2>
                <p style="font-size: 1.1rem; opacity: 0.9; margin-bottom: 2rem;">
                    الزر الآن بحجم مناسب ومتناسق مع تصميم النافبار
                </p>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0;">
                    <div>
                        <h4>✅ الحجم</h4>
                        <p>مناسب ومتناسق</p>
                    </div>
                    <div>
                        <h4>✅ التجاوب</h4>
                        <p>يعمل على جميع الأجهزة</p>
                    </div>
                    <div>
                        <h4>✅ التصميم</h4>
                        <p>أنيق ومتطابق</p>
                    </div>
                </div>
                
                <div style="margin-top: 2rem;">
                    <a href="index.html" class="btn" style="background: white; color: var(--primary-brown); margin: 0.5rem;">
                        <i class="fas fa-home"></i>
                        <span>اختبر الموقع العربي</span>
                    </a>
                    <a href="index-en.html" class="btn" style="background: white; color: var(--primary-brown); margin: 0.5rem;">
                        <i class="fas fa-globe"></i>
                        <span>اختبر الموقع الإنجليزي</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ اختبار زر "احجز الآن":');
            console.log('✅ الحجم: محسن ومناسب');
            console.log('✅ التجاوب: يعمل على جميع الأجهزة');
            console.log('✅ التصميم: متناسق مع النافبار');
            console.log('✅ الوظيفة: تعمل بشكل مثالي');
        });
    </script>
</body>
</html>
