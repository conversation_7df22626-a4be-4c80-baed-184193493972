<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - مطعم أوكتا</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body data-lang="ar">
    <!-- Navigation Test -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <!-- Logo -->
                <a href="#home" class="nav-logo">
                    <img src="images/logo.png" alt="Octa Restaurant Logo" class="logo-image" width="45" height="45">
                    <span class="logo-text">أوكتا</span>
                </a>

                <!-- Navigation Menu -->
                <ul class="nav-menu" id="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link active" data-section="home">
                            <i class="fas fa-home"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#about" class="nav-link" data-section="about">
                            <i class="fas fa-info-circle"></i>
                            <span>عن أوكتا</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#menu" class="nav-link" data-section="menu">
                            <i class="fas fa-utensils"></i>
                            <span>القائمة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link" data-section="contact">
                            <i class="fas fa-phone"></i>
                            <span>اتصل بنا</span>
                        </a>
                    </li>
                </ul>

                <!-- Controls -->
                <div class="nav-controls">
                    <!-- Language Toggle -->
                    <button class="lang-toggle" id="lang-toggle" aria-label="Switch to English">
                        <i class="fas fa-globe"></i>
                        <span>English</span>
                    </button>

                    <!-- Theme Toggle -->
                    <button class="theme-toggle" id="theme-toggle" aria-label="تبديل الوضع المظلم">
                        <i class="fas fa-moon" id="theme-icon"></i>
                    </button>

                    <!-- Mobile Menu Toggle -->
                    <button class="nav-toggle" id="nav-toggle" aria-label="فتح القائمة" aria-expanded="false">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>

                <!-- CTA Button (Desktop) -->
                <a href="#contact" class="nav-cta cta-btn">
                    <i class="fas fa-phone"></i>
                    <span>احجز الآن</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Mobile CTA -->
    <a href="#contact" class="mobile-cta cta-btn floating" aria-label="احجز الآن">
        <i class="fas fa-phone"></i>
    </a>

    <!-- Hero Section Test -->
    <section class="hero" id="home">
        <div class="hero-background">
            <div class="hero-particles" id="hero-particles"></div>
            <div class="hero-overlay"></div>
        </div>
        
        <div class="container">
            <div class="hero-content">
                <div class="hero-text fade-in-left">
                    <div class="hero-title">
                        <span class="title-line">مرحباً بكم في</span>
                        <span class="title-main">أوكتا</span>
                        <span class="title-subtitle">مطعم وكافيه</span>
                    </div>
                    <p class="hero-description">
                        اكتشف نكهات إيطاليا الأصيلة في قلب مصر. طهاتنا المتمرسون يقدمون الوصفات التقليدية 
                        بلمسة عصرية، باستخدام أجود المكونات لخلق تجارب طعام لا تُنسى.
                    </p>
                    <div class="hero-buttons">
                        <a href="#menu" class="btn btn-primary" data-scroll="menu">
                            <i class="fas fa-utensils"></i>
                            <span>استكشف القائمة</span>
                        </a>
                        <a href="#contact" class="btn btn-secondary" data-scroll="contact">
                            <i class="fas fa-calendar-alt"></i>
                            <span>احجز طاولة</span>
                        </a>
                    </div>
                </div>
                
                <div class="hero-image fade-in-right">
                    <div class="hero-image-container">
                        <img src="images/hero-dish.jpg" alt="طبق إيطالي مميز" loading="eager">
                        <div class="hero-image-accent"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Section -->
    <section class="test-section" style="padding: 4rem 0; background: var(--white);">
        <div class="container">
            <div class="section-header fade-in">
                <span class="section-subtitle">اختبار شامل</span>
                <h2 class="section-title">فحص جميع العناصر</h2>
                <p class="section-description">
                    اختبار شامل لجميع عناصر الموقع للتأكد من عملها بشكل صحيح
                </p>
            </div>

            <!-- Status Grid -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin: 3rem 0;">
                <div style="background: var(--light-gray); padding: 2rem; border-radius: 15px; text-align: center; border: 1px solid var(--elegant-border);">
                    <i class="fas fa-check-circle" style="color: var(--deep-green); font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3 style="color: var(--primary-brown); margin-bottom: 0.5rem;">النافبار</h3>
                    <p style="color: var(--gray);">تم إصلاح التنسيق والتناسق</p>
                </div>
                
                <div style="background: var(--light-gray); padding: 2rem; border-radius: 15px; text-align: center; border: 1px solid var(--elegant-border);">
                    <i class="fas fa-check-circle" style="color: var(--deep-green); font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3 style="color: var(--primary-brown); margin-bottom: 0.5rem;">التجاوب</h3>
                    <p style="color: var(--gray);">يعمل على جميع الأجهزة</p>
                </div>
                
                <div style="background: var(--light-gray); padding: 2rem; border-radius: 15px; text-align: center; border: 1px solid var(--elegant-border);">
                    <i class="fas fa-check-circle" style="color: var(--deep-green); font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3 style="color: var(--primary-brown); margin-bottom: 0.5rem;">الخطوط</h3>
                    <p style="color: var(--gray);">أحجام وأنواع محسنة</p>
                </div>
                
                <div style="background: var(--light-gray); padding: 2rem; border-radius: 15px; text-align: center; border: 1px solid var(--elegant-border);">
                    <i class="fas fa-check-circle" style="color: var(--deep-green); font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3 style="color: var(--primary-brown); margin-bottom: 0.5rem;">اللغات</h3>
                    <p style="color: var(--gray);">دعم كامل للعربية والإنجليزية</p>
                </div>
            </div>

            <!-- Button Tests -->
            <div style="margin: 3rem 0;">
                <h3 style="color: var(--primary-brown); margin-bottom: 2rem; text-align: center;">اختبار الأزرار</h3>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <button class="btn btn-primary">
                        <i class="fas fa-utensils"></i>
                        <span>زر أساسي</span>
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-heart"></i>
                        <span>زر ثانوي</span>
                    </button>
                    <button class="btn btn-outline">
                        <i class="fas fa-phone"></i>
                        <span>زر محدد</span>
                    </button>
                </div>
            </div>

            <!-- Typography Test -->
            <div style="margin: 3rem 0; background: var(--light-gray); padding: 2rem; border-radius: 15px;">
                <h3 style="color: var(--primary-brown); margin-bottom: 1rem;">اختبار الخطوط</h3>
                <h1 style="margin-bottom: 1rem;">عنوان رئيسي - H1</h1>
                <h2 style="margin-bottom: 1rem;">عنوان ثانوي - H2</h2>
                <h3 style="margin-bottom: 1rem;">عنوان فرعي - H3</h3>
                <p style="margin-bottom: 1rem;">
                    هذا نص تجريبي لاختبار الخطوط والتنسيق. يجب أن يكون واضحاً وسهل القراءة على جميع الأجهزة.
                    نحن نختبر التباعد بين الأسطر وحجم الخط والوضوح العام للنص.
                </p>
                <p style="font-size: 0.9rem; color: var(--gray);">
                    نص صغير للاختبار - يجب أن يكون مقروءاً أيضاً
                </p>
            </div>

            <!-- Navigation Links Test -->
            <div style="margin: 3rem 0;">
                <h3 style="color: var(--primary-brown); margin-bottom: 2rem; text-align: center;">اختبار روابط التنقل</h3>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="#home" class="nav-link" style="background: var(--light-gray); border-radius: 8px;">
                        <i class="fas fa-home"></i>
                        <span>الرئيسية</span>
                    </a>
                    <a href="#menu" class="nav-link" style="background: var(--light-gray); border-radius: 8px;">
                        <i class="fas fa-utensils"></i>
                        <span>القائمة</span>
                    </a>
                    <a href="#contact" class="nav-link" style="background: var(--light-gray); border-radius: 8px;">
                        <i class="fas fa-phone"></i>
                        <span>اتصل بنا</span>
                    </a>
                </div>
            </div>

            <!-- Final Status -->
            <div style="background: linear-gradient(135deg, var(--primary-brown), var(--deep-green)); color: white; padding: 3rem; border-radius: 20px; text-align: center; margin: 3rem 0;">
                <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <h2 style="margin-bottom: 1rem;">تم إصلاح جميع المشاكل!</h2>
                <p style="font-size: 1.1rem; opacity: 0.9;">
                    الموقع الآن يعمل بشكل مثالي على جميع الأجهزة مع تصميم متناسق وأنيق
                </p>
                <div style="margin-top: 2rem;">
                    <a href="index.html" class="btn" style="background: white; color: var(--primary-brown); margin: 0.5rem;">
                        <i class="fas fa-home"></i>
                        <span>الموقع الرئيسي</span>
                    </a>
                    <a href="index-en.html" class="btn" style="background: white; color: var(--primary-brown); margin: 0.5rem;">
                        <i class="fas fa-globe"></i>
                        <span>النسخة الإنجليزية</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="js/script.js"></script>
    <script>
        // Test script
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ الموقع تم تحميله بنجاح');
            console.log('✅ النافبار يعمل بشكل صحيح');
            console.log('✅ التجاوب محسن');
            console.log('✅ الخطوط محسنة');
            console.log('✅ جميع المشاكل تم إصلاحها');
            
            // Test navigation
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('تم النقر على:', this.textContent.trim());
                });
            });
            
            // Test buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('تم النقر على الزر:', this.textContent.trim());
                });
            });
        });
    </script>
</body>
</html>
