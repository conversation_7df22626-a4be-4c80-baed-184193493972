# 🚀 OCTA Restaurant - Quick Start Guide

## 📋 Prerequisites

Before you begin, ensure you have:
- A modern web browser (Chrome, Firefox, Safari, Edge)
- Node.js installed (for local development server)
- A code editor (VS Code recommended)

## ⚡ Quick Start (3 Steps)

### 1. Download & Extract
Download the project files and extract them to your desired location.

### 2. Start the Server
Choose one of these methods:

#### Method A: Double-click (Windows)
- Double-click `start-server.bat`
- The website will open automatically in your browser

#### Method B: Command Line (Windows/Mac/Linux)
```bash
# Windows
start-server.bat

# Mac/Linux
chmod +x start-server.sh
./start-server.sh
```

#### Method C: Manual
```bash
# Install http-server globally (one time only)
npm install -g http-server

# Start the server
http-server -p 8080 -o
```

### 3. View the Website
The website will automatically open at: `http://localhost:8080`

## 🌐 Language Versions

- **English (Primary)**: `http://localhost:8080/index.html`
- **Arabic (Secondary)**: `http://localhost:8080/ar.html`

## 📱 Testing on Mobile

### Local Network Access
1. Find your computer's IP address:
   - Windows: `ipconfig`
   - Mac/Linux: `ifconfig`
2. Access from mobile: `http://YOUR-IP:8080`

### Browser Developer Tools
1. Press `F12` in your browser
2. Click the mobile device icon
3. Select different device sizes to test

## 🛠️ Development Setup

### VS Code Setup
1. Open the project folder in VS Code
2. Install recommended extensions (VS Code will prompt you)
3. Use Live Server extension for auto-reload during development

### File Structure
```
octa-restaurant/
├── index.html          # English homepage
├── ar.html             # Arabic homepage
├── assets/
│   ├── css/styles.css  # Main stylesheet
│   ├── js/main.js      # Main JavaScript
│   └── images/         # All images
├── manifest.json       # PWA configuration
└── README.md          # Full documentation
```

## 🎨 Customization Quick Tips

### Change Colors
Edit `assets/css/styles.css` and modify these variables:
```css
:root {
    --primary-color: #d4af37;    /* Gold */
    --secondary-color: #8b4513;  /* Brown */
    --accent-color: #ff6b35;     /* Orange */
}
```

### Update Content
- **Menu items**: Edit `menuData` array in `assets/js/main.js`
- **Gallery images**: Edit `galleryData` array in `assets/js/main.js`
- **Contact info**: Update HTML files directly
- **Restaurant info**: Update HTML files directly

### Add Images
1. Place images in `assets/images/` folder
2. Update the corresponding HTML or JavaScript references
3. Optimize images for web (recommended: JPG for photos, PNG for logos)

## 📞 Contact Information

### Restaurant Locations
- **Cairo**: *********** (1 Talaat Harb, Tahrir Square)
- **New Cairo**: *********** (Muse Mall, North 90th Street)
- **Alexandria**: *********** (Army Road, San Stefano)

### Business Hours
All locations: **Daily 8:00 AM - 1:00 AM**

## 🔧 Troubleshooting

### Common Issues

#### "Cannot GET /" Error
- Make sure you're running a local server
- Don't open HTML files directly in browser
- Use one of the server methods above

#### Images Not Loading
- Check that images exist in `assets/images/` folder
- Verify image file names match the references in code
- Use placeholder images from Unsplash if needed

#### Mobile Menu Not Working
- Check that JavaScript is enabled
- Clear browser cache and reload
- Test in different browsers

#### Fonts Not Loading
- Check internet connection (fonts load from Google Fonts)
- Clear browser cache
- Check browser console for errors

### Getting Help
1. Check the full `README.md` for detailed documentation
2. Inspect browser console for error messages
3. Test in different browsers to isolate issues

## 🚀 Deployment

### For Production
1. Upload all files to your web server
2. Update URLs in `sitemap.xml` and `manifest.json`
3. Configure SSL certificate for HTTPS
4. Test all functionality on the live server

### Recommended Hosting
- **Static hosting**: Netlify, Vercel, GitHub Pages
- **Traditional hosting**: Any web hosting with HTML support
- **CDN**: Cloudflare for better performance

## ✅ Quick Checklist

Before going live, ensure:
- [ ] All images are optimized and loading
- [ ] Both language versions work correctly
- [ ] Contact forms are functional
- [ ] Google Maps links work
- [ ] Mobile responsiveness is tested
- [ ] All links and buttons work
- [ ] SEO meta tags are updated
- [ ] SSL certificate is configured

---

**🎉 You're ready to go! The OCTA Restaurant website is now running locally.**

For detailed documentation, see the full `README.md` file.
