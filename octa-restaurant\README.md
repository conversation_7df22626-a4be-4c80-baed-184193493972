# OCTA Restaurant & Café Website

A modern, responsive website for OCTA Restaurant & Café featuring authentic Italian cuisine. Built with clean HTML5, CSS3, and vanilla JavaScript.

## 🌟 Features

### 🌐 Multi-Language Support
- **Primary Language**: English (`index.html`)
- **Secondary Language**: Arabic (`ar.html`)
- Seamless language switching
- RTL support for Arabic

### 🎨 Modern Design
- Clean, professional design
- Responsive layout for all devices
- Dark/Light theme toggle
- Smooth animations and transitions
- Interactive elements

### 📱 Responsive Design
- Mobile-first approach
- Optimized for tablets and desktops
- Touch-friendly navigation
- Adaptive layouts

### 🍽️ Restaurant Features
- **Menu Section**: Dynamic menu with categories and filtering
- **Gallery**: Image gallery with lightbox functionality
- **Locations**: Three branch locations with Google Maps integration
- **Contact Form**: Functional contact form with validation
- **About Section**: Restaurant story and features

### ⚡ Performance Optimized
- Fast loading times
- Optimized images
- Minimal dependencies
- Clean, semantic code

## 📍 Restaurant Locations

### 1. Cairo Branch - Tahrir
- **Address**: 1 Talaat Harb Street, Tahrir Square, Cairo
- **Phone**: 01065888236
- **Hours**: Daily 8:00 AM - 1:00 AM

### 2. New Cairo Branch
- **Address**: Muse Mall, North 90th Street with El Sadat Axis, New Cairo
- **Phone**: 01007120990
- **Hours**: Daily 8:00 AM - 1:00 AM

### 3. Alexandria Branch - San Stefano
- **Address**: Army Road, San Stefano, Front of July 26 Hotel, Alexandria
- **Phone**: 01000351317
- **Hours**: Daily 8:00 AM - 1:00 AM

## 🛠️ Technical Stack

### Frontend
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern CSS with custom properties and grid/flexbox
- **JavaScript**: Vanilla ES6+ with modern features
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Inter, Playfair Display, Cairo, Amiri)

### Features
- **Intersection Observer API**: For scroll animations
- **CSS Grid & Flexbox**: Modern layouts
- **CSS Custom Properties**: Theme system
- **Local Storage**: Theme and preferences
- **Responsive Images**: Optimized loading

## 📁 Project Structure

```
octa-restaurant/
├── index.html              # English homepage
├── ar.html                 # Arabic homepage
├── assets/
│   ├── css/
│   │   └── styles.css      # Main stylesheet
│   ├── js/
│   │   └── main.js         # Main JavaScript
│   ├── images/
│   │   ├── logo.png
│   │   ├── hero-bg.jpg
│   │   ├── hero-dish.jpg
│   │   ├── about-main.jpg
│   │   ├── about-chef.jpg
│   │   ├── branch-cairo.jpg
│   │   ├── branch-newcairo.jpg
│   │   ├── branch-alexandria.jpg
│   │   ├── menu/
│   │   │   ├── pizza-margherita.jpg
│   │   │   ├── pasta-carbonara.jpg
│   │   │   ├── bruschetta.jpg
│   │   │   ├── tiramisu.jpg
│   │   │   ├── osso-buco.jpg
│   │   │   └── espresso.jpg
│   │   └── gallery/
│   │       ├── food-1.jpg
│   │       ├── food-2.jpg
│   │       ├── food-3.jpg
│   │       ├── interior-1.jpg
│   │       ├── interior-2.jpg
│   │       └── events-1.jpg
│   └── menu.pdf            # Full menu PDF
└── README.md               # This file
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser
- Local web server (optional, for development)

### Installation
1. Clone or download the project files
2. Place all files in your web server directory
3. Open `index.html` in your browser

### Development
For local development, you can use any local server:

```bash
# Using Python
python -m http.server 8000

# Using Node.js (http-server)
npx http-server

# Using PHP
php -S localhost:8000
```

## 🎯 Key Sections

### Navigation
- Fixed header with smooth scrolling
- Mobile-responsive hamburger menu
- Language and theme toggles
- Active section highlighting

### Hero Section
- Full-screen hero with background image
- Call-to-action buttons
- Animated content
- Scroll indicator

### About Section
- Restaurant story and philosophy
- Feature highlights with icons
- Image gallery grid
- Animated content on scroll

### Menu Section
- Category-based filtering
- Dynamic menu items
- Featured items highlighting
- Responsive grid layout

### Gallery Section
- Filter-based image gallery
- Lightbox functionality
- Keyboard navigation
- Touch-friendly interface

### Locations Section
- Three restaurant locations
- Google Maps integration
- Contact information
- Reservation buttons

### Contact Section
- Contact form with validation
- Multiple contact methods
- Social media links
- Business hours

### Footer
- Quick links
- Contact information
- Social media links
- Copyright information

## 🎨 Customization

### Colors
The color scheme can be customized by modifying CSS custom properties in `styles.css`:

```css
:root {
    --primary-color: #d4af37;
    --primary-dark: #b8941f;
    --primary-light: #e6c866;
    --secondary-color: #8b4513;
    --accent-color: #ff6b35;
}
```

### Fonts
Fonts can be changed by updating the Google Fonts imports and CSS font families.

### Content
- Update menu items in `main.js` (`menuData` array)
- Update gallery images in `main.js` (`galleryData` array)
- Modify text content in HTML files
- Replace images in the `assets/images/` directory

## 📱 Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Features in Detail

### Theme System
- Light and dark themes
- Persistent theme selection
- Smooth theme transitions
- System preference detection

### Language System
- English (primary) and Arabic support
- RTL layout for Arabic
- Appropriate fonts for each language
- Seamless language switching

### Animation System
- Intersection Observer for scroll animations
- CSS transitions and transforms
- Loading screen animation
- Hover effects and micro-interactions

### Form Handling
- Client-side form validation
- Success/error notifications
- Accessible form labels
- Mobile-friendly inputs

## 📞 Contact Information

- **Email**: <EMAIL>
- **Reservations**: <EMAIL>
- **Phone Numbers**:
  - Cairo: 01065888236
  - New Cairo: 01007120990
  - Alexandria: 01000351317

## 📄 License

This project is created for OCTA Restaurant & Café. All rights reserved.

## 🤝 Contributing

This is a commercial project for OCTA Restaurant & Café. For any modifications or updates, please contact the development team.

---

**Built with ❤️ for OCTA Restaurant & Café**
