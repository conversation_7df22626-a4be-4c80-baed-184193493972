<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدارك مود للهواتف - أوكتا</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        :root {
            --primary-brown: #A66C42;
            --deep-green: #216354;
            --white: #FFFFFF;
            --black: #1A1A1A;
            --light-gray: #F8F8F8;
        }

        [data-theme="dark"] {
            --white: #1A1A1A;
            --black: #FFFFFF;
            --light-gray: #2A2A2A;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--white);
            color: var(--black);
            transition: all 0.3s ease;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        [data-theme="dark"] .header {
            background: rgba(26, 26, 26, 0.95);
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-brown);
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .theme-toggle, .menu-toggle {
            background: none;
            border: none;
            color: var(--black);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover, .menu-toggle:hover {
            background: rgba(166, 108, 66, 0.1);
            color: var(--primary-brown);
        }

        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 999;
            transition: left 0.4s ease;
        }

        [data-theme="dark"] .mobile-menu {
            background: rgba(26, 26, 26, 0.98);
        }

        .mobile-menu.active {
            left: 0;
        }

        .menu-item {
            margin: 1rem 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .mobile-menu.active .menu-item {
            opacity: 1;
            transform: translateY(0);
        }

        .mobile-menu.active .menu-item:nth-child(1) { transition-delay: 0.1s; }
        .mobile-menu.active .menu-item:nth-child(2) { transition-delay: 0.2s; }
        .mobile-menu.active .menu-item:nth-child(3) { transition-delay: 0.3s; }
        .mobile-menu.active .menu-item:nth-child(4) { transition-delay: 0.4s; }
        .mobile-menu.active .menu-item:nth-child(5) { transition-delay: 0.5s; }

        .menu-link {
            display: block;
            padding: 1rem 2rem;
            background: rgba(166, 108, 66, 0.1);
            color: var(--black);
            text-decoration: none;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
            width: 250px;
            transition: all 0.3s ease;
        }

        [data-theme="dark"] .menu-link {
            background: rgba(166, 108, 66, 0.15);
            color: var(--black);
        }

        .menu-link:hover {
            background: linear-gradient(135deg, var(--primary-brown), var(--deep-green));
            color: white;
            transform: translateY(-2px);
        }

        /* Content */
        .content {
            margin-top: 80px;
            padding: 2rem 1rem;
            min-height: calc(100vh - 80px);
        }

        .test-section {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .status {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            font-weight: bold;
        }

        .success {
            background: rgba(33, 99, 84, 0.1);
            color: var(--deep-green);
            border: 2px solid var(--deep-green);
        }

        .info {
            background: rgba(166, 108, 66, 0.1);
            color: var(--primary-brown);
            border: 2px solid var(--primary-brown);
        }

        .test-button {
            background: var(--primary-brown);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background: var(--deep-green);
            transform: translateY(-2px);
        }

        /* Hamburger Animation */
        .hamburger {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .hamburger-line {
            width: 25px;
            height: 3px;
            background: var(--primary-brown);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .menu-toggle.active .hamburger-line:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .menu-toggle.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .menu-toggle.active .hamburger-line:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">أوكتا</div>
        <div class="controls">
            <button class="theme-toggle" id="theme-toggle" aria-label="تبديل الوضع المظلم">
                <i class="fas fa-moon" id="theme-icon"></i>
            </button>
            <button class="menu-toggle" id="menu-toggle" aria-label="فتح القائمة">
                <div class="hamburger">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </div>
            </button>
        </div>
    </div>

    <div class="mobile-menu" id="mobile-menu">
        <div class="menu-item">
            <a href="#" class="menu-link">الرئيسية</a>
        </div>
        <div class="menu-item">
            <a href="#" class="menu-link">عن أوكتا</a>
        </div>
        <div class="menu-item">
            <a href="#" class="menu-link">القائمة</a>
        </div>
        <div class="menu-item">
            <a href="#" class="menu-link">المعرض</a>
        </div>
        <div class="menu-item">
            <a href="#" class="menu-link">اتصل بنا</a>
        </div>
    </div>

    <div class="content">
        <div class="test-section">
            <h2>🌙 اختبار الدارك مود للهواتف</h2>
            <p>هذه الصفحة مخصصة لاختبار الدارك مود في القائمة المحمولة</p>
        </div>

        <div class="test-section">
            <h3>📱 خطوات الاختبار</h3>
            <ol style="text-align: right; padding-right: 2rem;">
                <li>اضغط على أيقونة القمر لتفعيل الدارك مود</li>
                <li>اضغط على أيقونة الهامبرغر لفتح القائمة</li>
                <li>تأكد من أن القائمة تظهر بالوضع المظلم</li>
                <li>جرب التبديل بين الأوضاع والقائمة مفتوحة</li>
                <li>أغلق القائمة وجرب مرة أخرى</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎯 الحالة الحالية</h3>
            <div class="status success" id="theme-status">
                ✅ الوضع الحالي: فاتح
            </div>
            <div class="status info" id="menu-status">
                📱 القائمة: مغلقة
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات سريعة</h3>
            <button class="test-button" onclick="testDarkMode()">اختبار الدارك مود</button>
            <button class="test-button" onclick="testMobileMenu()">اختبار القائمة المحمولة</button>
            <button class="test-button" onclick="testBoth()">اختبار كلاهما معاً</button>
        </div>

        <div class="test-section">
            <h3>📋 نتائج الاختبار</h3>
            <div id="test-results">
                <p>لم يتم تشغيل أي اختبار بعد</p>
            </div>
        </div>
    </div>

    <script>
        let isDarkMode = false;
        let isMenuOpen = false;

        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        const themeStatus = document.getElementById('theme-status');
        const menuStatus = document.getElementById('menu-status');
        const testResults = document.getElementById('test-results');

        // Theme toggle
        themeToggle.addEventListener('click', () => {
            isDarkMode = !isDarkMode;
            const theme = isDarkMode ? 'dark' : 'light';
            
            document.documentElement.setAttribute('data-theme', theme);
            themeIcon.className = isDarkMode ? 'fas fa-sun' : 'fas fa-moon';
            
            updateStatus();
        });

        // Menu toggle
        menuToggle.addEventListener('click', () => {
            isMenuOpen = !isMenuOpen;
            
            menuToggle.classList.toggle('active');
            mobileMenu.classList.toggle('active');
            
            if (isMenuOpen) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
            
            updateStatus();
        });

        // Close menu when clicking outside
        mobileMenu.addEventListener('click', (e) => {
            if (e.target === mobileMenu) {
                isMenuOpen = false;
                menuToggle.classList.remove('active');
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
                updateStatus();
            }
        });

        function updateStatus() {
            themeStatus.innerHTML = `${isDarkMode ? '🌙' : '☀️'} الوضع الحالي: ${isDarkMode ? 'مظلم' : 'فاتح'}`;
            menuStatus.innerHTML = `📱 القائمة: ${isMenuOpen ? 'مفتوحة' : 'مغلقة'}`;
        }

        function testDarkMode() {
            const results = [];
            
            // Test 1: Toggle dark mode
            themeToggle.click();
            results.push(`✅ تم تفعيل الدارك مود: ${isDarkMode ? 'نجح' : 'فشل'}`);
            
            // Test 2: Check background color
            const bgColor = getComputedStyle(document.body).backgroundColor;
            results.push(`✅ لون الخلفية تغير: ${bgColor.includes('26') ? 'نجح' : 'فشل'}`);
            
            displayResults(results);
        }

        function testMobileMenu() {
            const results = [];
            
            // Test 1: Open menu
            menuToggle.click();
            results.push(`✅ فتح القائمة: ${isMenuOpen ? 'نجح' : 'فشل'}`);
            
            // Test 2: Check menu visibility
            const menuVisible = mobileMenu.classList.contains('active');
            results.push(`✅ ظهور القائمة: ${menuVisible ? 'نجح' : 'فشل'}`);
            
            // Test 3: Check dark mode in menu
            if (isDarkMode) {
                const menuBg = getComputedStyle(mobileMenu).backgroundColor;
                results.push(`✅ الدارك مود في القائمة: ${menuBg.includes('26') ? 'نجح' : 'فشل'}`);
            }
            
            displayResults(results);
        }

        function testBoth() {
            const results = [];
            
            // Test sequence
            results.push('🧪 بدء الاختبار الشامل...');
            
            // Step 1: Enable dark mode
            if (!isDarkMode) {
                themeToggle.click();
            }
            results.push(`1️⃣ تفعيل الدارك مود: ${isDarkMode ? 'نجح' : 'فشل'}`);
            
            // Step 2: Open menu
            setTimeout(() => {
                if (!isMenuOpen) {
                    menuToggle.click();
                }
                results.push(`2️⃣ فتح القائمة: ${isMenuOpen ? 'نجح' : 'فشل'}`);
                
                // Step 3: Check menu dark mode
                const menuBg = getComputedStyle(mobileMenu).backgroundColor;
                const isDarkMenu = menuBg.includes('26');
                results.push(`3️⃣ الدارك مود في القائمة: ${isDarkMenu ? 'نجح ✅' : 'فشل ❌'}`);
                
                displayResults(results);
            }, 500);
        }

        function displayResults(results) {
            testResults.innerHTML = results.map(result => `<p>${result}</p>`).join('');
        }

        // Initialize
        updateStatus();
    </script>
</body>
</html>
