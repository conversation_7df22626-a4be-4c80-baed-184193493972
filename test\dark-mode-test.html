<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدارك مود - أوكتا</title>
    <style>
        :root {
            --primary-brown: #A66C42;
            --deep-green: #216354;
            --white: #FFFFFF;
            --black: #1A1A1A;
            --light-gray: #F8F8F8;
        }

        [data-theme="dark"] {
            --white: #1A1A1A;
            --black: #FFFFFF;
            --light-gray: #2A2A2A;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--white);
            color: var(--black);
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .test-container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }

        .theme-toggle {
            background: var(--primary-brown);
            color: var(--white);
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 2rem 0;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--deep-green);
            transform: translateY(-2px);
        }

        .test-card {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 15px;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }

        .status {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-weight: bold;
        }

        .success {
            background: rgba(33, 99, 84, 0.1);
            color: var(--deep-green);
            border: 2px solid var(--deep-green);
        }

        .error {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 2px solid #e74c3c;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌙 اختبار الدارك مود</h1>
        <p>اضغط على الزر لتجربة تبديل الوضع المظلم</p>
        
        <button class="theme-toggle" onclick="toggleTheme()">
            <span id="theme-text">🌙 تفعيل الوضع المظلم</span>
        </button>

        <div class="test-card">
            <h3>معلومات الوضع الحالي</h3>
            <p id="current-theme">الوضع الحالي: فاتح</p>
            <p id="theme-stored">المحفوظ في التخزين: <span id="storage-value">غير محدد</span></p>
        </div>

        <div class="test-card">
            <h3>اختبار الألوان</h3>
            <div style="display: flex; gap: 1rem; justify-content: center; margin: 1rem 0;">
                <div style="width: 50px; height: 50px; background: var(--primary-brown); border-radius: 10px;"></div>
                <div style="width: 50px; height: 50px; background: var(--deep-green); border-radius: 10px;"></div>
                <div style="width: 50px; height: 50px; background: var(--white); border: 2px solid var(--black); border-radius: 10px;"></div>
                <div style="width: 50px; height: 50px; background: var(--black); border-radius: 10px;"></div>
            </div>
        </div>

        <div id="status" class="status success">
            ✅ الدارك مود يعمل بشكل صحيح!
        </div>

        <div class="test-card">
            <h3>التعليمات</h3>
            <ol style="text-align: right;">
                <li>اضغط على زر تبديل الوضع</li>
                <li>لاحظ تغيير الألوان</li>
                <li>تحقق من حفظ التفضيل</li>
                <li>أعد تحميل الصفحة للتأكد من الحفظ</li>
            </ol>
        </div>
    </div>

    <script>
        // Theme management
        function initTheme() {
            const savedTheme = localStorage.getItem('octa-theme') || 'light';
            setTheme(savedTheme);
            updateUI();
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
            updateUI();
        }

        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('octa-theme', theme);
        }

        function updateUI() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const themeText = document.getElementById('theme-text');
            const currentThemeEl = document.getElementById('current-theme');
            const storageValue = document.getElementById('storage-value');
            const status = document.getElementById('status');

            // Update button text
            themeText.textContent = currentTheme === 'dark' ? '☀️ تفعيل الوضع الفاتح' : '🌙 تفعيل الوضع المظلم';

            // Update current theme display
            currentThemeEl.textContent = `الوضع الحالي: ${currentTheme === 'dark' ? 'مظلم' : 'فاتح'}`;

            // Update storage value
            const stored = localStorage.getItem('octa-theme');
            storageValue.textContent = stored || 'غير محدد';

            // Update status
            if (currentTheme === 'dark') {
                status.className = 'status success';
                status.innerHTML = '🌙 الوضع المظلم مفعل بنجاح!';
            } else {
                status.className = 'status success';
                status.innerHTML = '☀️ الوضع الفاتح مفعل بنجاح!';
            }
        }

        // Test functions
        function testThemeToggle() {
            console.log('Testing theme toggle...');
            
            // Test 1: Initial state
            console.log('Initial theme:', document.documentElement.getAttribute('data-theme'));
            
            // Test 2: Toggle to dark
            toggleTheme();
            console.log('After toggle:', document.documentElement.getAttribute('data-theme'));
            
            // Test 3: Check localStorage
            console.log('Stored theme:', localStorage.getItem('octa-theme'));
            
            // Test 4: Toggle back
            setTimeout(() => {
                toggleTheme();
                console.log('After second toggle:', document.documentElement.getAttribute('data-theme'));
            }, 1000);
        }

        // Initialize on load
        document.addEventListener('DOMContentLoaded', initTheme);

        // Add test button
        setTimeout(() => {
            const testBtn = document.createElement('button');
            testBtn.textContent = '🧪 تشغيل اختبار تلقائي';
            testBtn.style.cssText = `
                background: #e74c3c;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 15px;
                cursor: pointer;
                margin: 1rem;
            `;
            testBtn.onclick = testThemeToggle;
            document.querySelector('.test-container').appendChild(testBtn);
        }, 1000);
    </script>
</body>
</html>
