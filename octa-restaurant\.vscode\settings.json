{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": true}, "html.format.indentInnerHtml": true, "html.format.wrapLineLength": 120, "css.validate": true, "css.lint.unknownAtRules": "ignore", "emmet.includeLanguages": {"html": "html", "css": "css"}, "files.associations": {"*.html": "html", "*.css": "css", "*.js": "javascript"}, "liveServer.settings.port": 8080, "liveServer.settings.root": "/", "liveServer.settings.CustomBrowser": "chrome", "liveServer.settings.donotShowInfoMsg": true, "editor.wordWrap": "on", "editor.tabSize": 2, "editor.insertSpaces": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "html.suggest.html5": true, "css.completion.completePropertyWithSemicolon": true, "css.completion.triggerPropertyValueCompletion": true, "javascript.suggest.autoImports": true, "javascript.updateImportsOnFileMove.enabled": "always"}