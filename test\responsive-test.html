<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التجاوب - أوكتا</title>
    <style>
        :root {
            --primary-brown: #A66C42;
            --deep-green: #216354;
            --white: #FFFFFF;
            --black: #1A1A1A;
            --light-gray: #F8F8F8;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--white);
            color: var(--black);
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-brown), var(--deep-green));
            color: white;
            border-radius: 15px;
        }

        .breakpoint-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .breakpoint-card {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .breakpoint-card.active {
            border-color: var(--primary-brown);
            background: rgba(166, 108, 66, 0.1);
        }

        .screen-info {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .test-item {
            background: var(--primary-brown);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .test-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(166, 108, 66, 0.3);
        }

        .mobile-test {
            display: none;
        }

        .tablet-test {
            display: none;
        }

        .desktop-test {
            display: block;
        }

        /* Responsive breakpoints */
        @media (max-width: 1200px) {
            .desktop-test { display: none; }
            .large-tablet-test { display: block; }
        }

        @media (max-width: 1024px) {
            .large-tablet-test { display: none; }
            .tablet-test { display: block; }
            
            .test-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .tablet-test { display: none; }
            .mobile-test { display: block; }
            
            body {
                padding: 1rem;
            }
            
            .test-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .breakpoint-info {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .test-item {
                padding: 1.5rem;
            }
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }

        .status-good { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📱 اختبار التجاوب - أوكتا</h1>
            <p>اختبار شامل للتصميم المتجاوب على جميع الأجهزة</p>
        </div>

        <div class="screen-info">
            <h3>معلومات الشاشة الحالية</h3>
            <p><strong>العرض:</strong> <span id="screen-width">-</span> بكسل</p>
            <p><strong>الارتفاع:</strong> <span id="screen-height">-</span> بكسل</p>
            <p><strong>نوع الجهاز:</strong> <span id="device-type">-</span></p>
            <p><strong>الاتجاه:</strong> <span id="orientation">-</span></p>
        </div>

        <div class="breakpoint-info">
            <div class="breakpoint-card" id="mobile-card">
                <h4>📱 الهواتف</h4>
                <p>أقل من 768px</p>
                <span class="status-indicator" id="mobile-status"></span>
            </div>
            <div class="breakpoint-card" id="tablet-card">
                <h4>📟 الأجهزة اللوحية</h4>
                <p>768px - 1024px</p>
                <span class="status-indicator" id="tablet-status"></span>
            </div>
            <div class="breakpoint-card" id="desktop-card">
                <h4>🖥️ سطح المكتب</h4>
                <p>أكبر من 1024px</p>
                <span class="status-indicator" id="desktop-status"></span>
            </div>
        </div>

        <div class="desktop-test">
            <h3>🖥️ عرض سطح المكتب</h3>
            <p>التخطيط مُحسن للشاشات الكبيرة</p>
        </div>

        <div class="large-tablet-test">
            <h3>📱 عرض الجهاز اللوحي الكبير</h3>
            <p>التخطيط مُحسن للأجهزة اللوحية الكبيرة</p>
        </div>

        <div class="tablet-test">
            <h3>📟 عرض الجهاز اللوحي</h3>
            <p>التخطيط مُحسن للأجهزة اللوحية</p>
        </div>

        <div class="mobile-test">
            <h3>📱 عرض الهاتف المحمول</h3>
            <p>التخطيط مُحسن للهواتف الذكية</p>
        </div>

        <div class="test-grid">
            <div class="test-item">عنصر 1</div>
            <div class="test-item">عنصر 2</div>
            <div class="test-item">عنصر 3</div>
            <div class="test-item">عنصر 4</div>
            <div class="test-item">عنصر 5</div>
            <div class="test-item">عنصر 6</div>
        </div>

        <div style="margin-top: 3rem; padding: 2rem; background: var(--light-gray); border-radius: 15px;">
            <h3>🧪 نصائح الاختبار</h3>
            <ul style="text-align: right; padding-right: 2rem;">
                <li>غير حجم نافذة المتصفح لاختبار النقاط المختلفة</li>
                <li>استخدم أدوات المطور (F12) لمحاكاة الأجهزة المختلفة</li>
                <li>اختبر في الوضع العمودي والأفقي</li>
                <li>تأكد من سهولة القراءة والتنقل</li>
                <li>اختبر على أجهزة حقيقية عند الإمكان</li>
            </ul>
        </div>
    </div>

    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            document.getElementById('screen-width').textContent = width;
            document.getElementById('screen-height').textContent = height;
            
            // Determine device type
            let deviceType = '';
            let activeCard = '';
            
            if (width < 480) {
                deviceType = 'هاتف صغير';
                activeCard = 'mobile';
            } else if (width < 768) {
                deviceType = 'هاتف محمول';
                activeCard = 'mobile';
            } else if (width < 1024) {
                deviceType = 'جهاز لوحي';
                activeCard = 'tablet';
            } else if (width < 1200) {
                deviceType = 'جهاز لوحي كبير';
                activeCard = 'tablet';
            } else {
                deviceType = 'سطح المكتب';
                activeCard = 'desktop';
            }
            
            document.getElementById('device-type').textContent = deviceType;
            
            // Update orientation
            const orientation = width > height ? 'أفقي' : 'عمودي';
            document.getElementById('orientation').textContent = orientation;
            
            // Update active card
            document.querySelectorAll('.breakpoint-card').forEach(card => {
                card.classList.remove('active');
            });
            
            if (activeCard) {
                document.getElementById(activeCard + '-card').classList.add('active');
            }
            
            // Update status indicators
            updateStatusIndicators(width);
        }
        
        function updateStatusIndicators(width) {
            const mobileStatus = document.getElementById('mobile-status');
            const tabletStatus = document.getElementById('tablet-status');
            const desktopStatus = document.getElementById('desktop-status');
            
            // Reset all
            [mobileStatus, tabletStatus, desktopStatus].forEach(status => {
                status.className = 'status-indicator';
            });
            
            if (width < 768) {
                mobileStatus.classList.add('status-good');
                tabletStatus.classList.add('status-warning');
                desktopStatus.classList.add('status-error');
            } else if (width < 1024) {
                mobileStatus.classList.add('status-warning');
                tabletStatus.classList.add('status-good');
                desktopStatus.classList.add('status-warning');
            } else {
                mobileStatus.classList.add('status-error');
                tabletStatus.classList.add('status-warning');
                desktopStatus.classList.add('status-good');
            }
        }
        
        // Update on load and resize
        window.addEventListener('load', updateScreenInfo);
        window.addEventListener('resize', updateScreenInfo);
        
        // Update every second for real-time info
        setInterval(updateScreenInfo, 1000);
    </script>
</body>
</html>
