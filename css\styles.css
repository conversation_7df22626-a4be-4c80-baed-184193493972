/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Performance optimizations */
*,
*::before,
*::after {
    box-sizing: border-box;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

:root {
    /* Octa Brand Color Palette - Warm and Natural */
    --primary-brown: #A66C42;      /* Warm Brown - Primary CTA and headings */
    --secondary-brown: #A4693E;    /* Light Brown/Beige - Supporting elements */
    --tertiary-brown: #A56A40;     /* Light Brown/Beige - Subtle accents */
    --deep-green: #216354;         /* Deep Green - Secondary accent color */
    --white: #FFFFFF;              /* White - Clean backgrounds and text */
    --black: #2C2C2C;              /* Dark text for readability */
    --gray: #666666;               /* Medium gray for secondary text */
    --light-gray: #F8F8F8;         /* Light backgrounds */
    --cream: #FAF7F2;              /* Warm cream background */
    --beige: #F5F1EB;              /* Warm beige background */

    /* Additional brand colors for better contrast and accessibility */
    --brown-light: rgba(166, 108, 66, 0.1);    /* Light brown overlay */
    --brown-medium: rgba(166, 108, 66, 0.2);   /* Medium brown overlay */
    --green-light: rgba(33, 99, 84, 0.1);      /* Light green overlay */
    --text-muted: #8B8B8B;                     /* Muted text color */
    
    /* Typography - Following Octa Brand Guidelines */
    --font-headings: 'Playfair Display', 'Lora', serif;  /* Elegant serif for headings */
    --font-body: 'Open Sans', 'Cairo', sans-serif;       /* Clean sans-serif for body text */
    --font-arabic: 'Cairo', 'Open Sans', sans-serif;     /* Arabic font with fallback */

    /* Font weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Spacing */
    --section-padding: 80px 0;
    --container-padding: 0 20px;
    --border-radius: 8px;
    
    /* Shadows */
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--black);
    background-color: var(--white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* Typography Hierarchy - Following Octa Brand Guidelines */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-headings);
    font-weight: var(--font-weight-semibold);
    margin-bottom: 1rem;
    color: var(--primary-brown);
    line-height: 1.3;
    letter-spacing: -0.02em;
}

h1 {
    font-size: 3rem;
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
}

h2 {
    font-size: 2.5rem;
    font-weight: var(--font-weight-semibold);
}

h3 {
    font-size: 2rem;
    font-weight: var(--font-weight-semibold);
}

h4 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-medium);
}

h5 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-medium);
}

h6 {
    font-size: 1.125rem;
    font-weight: var(--font-weight-medium);
}

/* Body text */
body, p, span, div, li, input, textarea, select, button {
    font-family: var(--font-body);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
}

/* Arabic text styling */
[lang="ar"], .arabic-text {
    font-family: var(--font-arabic);
    direction: rtl;
    text-align: right;
}

/* English headings */
.english-heading {
    font-family: var(--font-headings);
    direction: ltr;
    text-align: left;
}

/* Text utilities */
.text-muted {
    color: var(--text-muted);
}

.text-small {
    font-size: 0.875rem;
}

.text-large {
    font-size: 1.125rem;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-brown);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-brown), var(--deep-green));
    border-radius: 2px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-arabic);
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-brown), var(--secondary-brown));
    color: var(--white);
    font-family: var(--font-body);
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--deep-green), var(--primary-brown));
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-brown);
    border: 2px solid var(--primary-brown);
}

.btn-secondary:hover {
    background: var(--primary-brown);
    color: var(--white);
    transform: translateY(-2px);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 4px 30px rgba(166, 108, 66, 0.08);
    border-bottom: 1px solid rgba(166, 108, 66, 0.08);
    transition: all 0.3s ease;
}

/* Sticky header when scrolled */
.navbar.scrolled {
    padding: 0.5rem 0;
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 30px rgba(166, 108, 66, 0.15);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

/* Logo Styles */
.nav-logo {
    display: flex;
    align-items: center;
    z-index: 1001;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 12px;
}

.logo-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(166, 108, 66, 0.15);
}

.logo-image {
    height: 45px;
    width: auto;
    object-fit: contain;
    transition: all 0.3s ease;
}

.navbar.scrolled .logo-image {
    height: 35px;
}

.logo-text {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-brown);
    font-family: var(--font-headings);
    letter-spacing: 2px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.navbar.scrolled .logo-text {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--black);
    font-weight: 500;
    font-family: var(--font-body);
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-link:hover {
    color: var(--primary-brown);
    background: rgba(166, 108, 66, 0.1);
    transform: translateY(-2px);
}

/* CTA Button Styles */
.nav-cta {
    display: flex;
    align-items: center;
    margin-left: 1rem;
}

.cta-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-brown), var(--deep-green));
    color: var(--white);
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-family: var(--font-body);
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(166, 108, 66, 0.2);
    border: 2px solid transparent;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(166, 108, 66, 0.3);
    background: linear-gradient(135deg, var(--deep-green), var(--primary-brown));
}

.cta-btn i {
    font-size: 1rem;
}

.navbar.scrolled .cta-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: var(--primary-brown);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Mobile Menu Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    z-index: 1002;
}

.nav-toggle:hover {
    background: rgba(166, 108, 66, 0.1);
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-brown);
    margin: 3px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* Hamburger Animation */
.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Responsive Design - Mobile First Approach */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
        position: relative;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.98);
        width: 100%;
        height: calc(100vh - 70px);
        text-align: center;
        transition: left 0.3s ease-in-out;
        box-shadow: 0 10px 27px rgba(166, 108, 66, 0.15);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border-top: 1px solid rgba(166, 108, 66, 0.1);
        padding: 2rem 0;
        z-index: 999;
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 0.5rem 0;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease;
    }

    .nav-menu.active .nav-item {
        opacity: 1;
        transform: translateY(0);
    }

    .nav-menu.active .nav-item:nth-child(1) { transition-delay: 0.1s; }
    .nav-menu.active .nav-item:nth-child(2) { transition-delay: 0.2s; }
    .nav-menu.active .nav-item:nth-child(3) { transition-delay: 0.3s; }
    .nav-menu.active .nav-item:nth-child(4) { transition-delay: 0.4s; }
    .nav-menu.active .nav-item:nth-child(5) { transition-delay: 0.5s; }
    .nav-menu.active .nav-item:nth-child(6) { transition-delay: 0.6s; }
    .nav-menu.active .nav-item:nth-child(7) { transition-delay: 0.7s; }

    .nav-link {
        font-size: 1.1rem;
        padding: 1rem 2rem;
        border-radius: 12px;
        margin: 0 1rem;
        display: block;
        font-weight: var(--font-weight-medium);
    }

    .nav-link:hover {
        background: var(--brown-light);
        transform: translateY(0);
    }

    .nav-toggle {
        display: flex;
        z-index: 1001;
    }

    .nav-cta {
        display: none; /* Hide in mobile menu, show as floating button */
    }

    /* Floating CTA button for mobile */
    .mobile-cta {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 1003;
        display: block;
    }

    .mobile-cta .cta-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        box-shadow: 0 8px 30px rgba(166, 108, 66, 0.4);
        border-radius: 50px;
    }

    .logo-image {
        height: 40px;
    }

    .logo-text {
        font-size: 1.6rem;
    }

    /* Prevent body scroll when menu is open */
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 0.5rem;
    }
    
    .logo-image {
        height: 35px;
    }
    
    .logo-text {
        font-size: 1.4rem;
        letter-spacing: 1px;
    }
    
    .cta-btn {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .nav-menu {
        top: 65px;
        padding: 1.5rem 0;
    }
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--cream), var(--beige));
    display: flex;
    align-items: center;
    min-height: 100vh;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(166, 108, 66, 0.05), rgba(33, 99, 84, 0.05));
    pointer-events: none;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--primary-brown);
    margin-bottom: 1rem;
    font-weight: 700;
    font-family: var(--font-headings);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--gray);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: var(--shadow-heavy);
}

/* Features Section */
.features {
    padding: var(--section-padding);
    background: var(--white);
    position: relative;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, var(--cream) 0%, var(--white) 50%, var(--beige) 100%);
    opacity: 0.3;
    pointer-events: none;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: var(--border-radius);
    background: var(--light-gray);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    font-size: 3rem;
    color: var(--deep-green);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    color: var(--primary-brown);
    transform: scale(1.1);
}

.feature-card h3 {
    color: var(--primary-brown);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--gray);
    line-height: 1.6;
}

/* About Section */
.about {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--white), var(--beige));
    position: relative;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at 70% 30%, rgba(33, 99, 84, 0.05), transparent 70%);
    pointer-events: none;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--gray);
    margin-bottom: 1.5rem;
}

.about-values {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.value-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--primary-brown);
    font-weight: 500;
}

.value-item i {
    font-size: 1.2rem;
    color: var(--olive-green);
}

.about-image img,
.about-img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: var(--border-radius);
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.about-image picture {
    display: block;
    width: 100%;
}

/* Enhanced image loading states */
img[loading="lazy"] {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

img.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* WebP support indicator */
.webp-supported picture source[type="image/webp"] {
    display: block;
}

.no-webp picture source[type="image/webp"] {
    display: none;
}

/* Menu Section */
.menu {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--beige), var(--cream));
    position: relative;
}

.menu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23A66C42" opacity="0.1"/></svg>') repeat;
    pointer-events: none;
}

/* Original Menu PDF Section */
.original-menu {
    padding: var(--section-padding);
    background: var(--beige);
}

.pdf-menu-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: start;
}

.pdf-menu-info h3 {
    color: var(--primary-brown);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.pdf-menu-info p {
    color: var(--gray);
    line-height: 1.8;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.pdf-menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.pdf-menu-buttons .btn {
    width: 100%;
    justify-content: center;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-outline {
    background: transparent;
    color: var(--primary-brown);
    border: 2px solid var(--primary-brown);
    padding: 12px 30px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-arabic);
    font-size: 1rem;
}

.btn-outline:hover {
    background: var(--primary-brown);
    color: var(--white);
    transform: translateY(-2px);
}

.pdf-preview {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-medium);
}

.pdf-embed-container {
    position: relative;
    width: 100%;
    min-height: 600px;
}

.pdf-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 600px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    border: 2px dashed var(--primary-brown);
}

.placeholder-content {
    text-align: center;
    color: var(--gray);
}

.placeholder-content i {
    font-size: 4rem;
    color: var(--primary-brown);
    margin-bottom: 1rem;
}

.placeholder-content h4 {
    color: var(--primary-brown);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.placeholder-content p {
    color: var(--gray);
    font-size: 1rem;
}

.menu-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
    position: relative;
    z-index: 1;
}

.tab-btn {
    padding: 12px 24px;
    border: 2px solid var(--primary-brown);
    background: transparent;
    color: var(--primary-brown);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-family: var(--font-arabic);
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-brown);
    color: var(--white);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.menu-item {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.menu-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.menu-item-content {
    padding: 1.5rem;
}

.menu-item h3 {
    color: var(--primary-brown);
    margin-bottom: 0.5rem;
}

.menu-item p {
    color: var(--gray);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.price {
    font-size: 1.2rem;
    font-weight: var(--font-weight-semibold);
    color: var(--deep-green);
    background: var(--green-light);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    display: inline-block;
}

/* Branches Section */
.branches {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--cream), var(--beige));
    position: relative;
}

.branches::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(166, 108, 66, 0.03), rgba(33, 99, 84, 0.03), rgba(166, 108, 66, 0.03));
    pointer-events: none;
}

.branches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.branch-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.branch-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.branch-header {
    margin-bottom: 1.5rem;
}

.branch-header h3 {
    color: var(--primary-brown);
    margin-bottom: 0.5rem;
}

.branch-city {
    color: var(--olive-green);
    font-weight: 500;
}

.branch-info {
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    color: var(--gray);
}

.info-item i {
    color: var(--primary-brown);
    margin-top: 2px;
    flex-shrink: 0;
}

.view-map {
    width: 100%;
}

/* Gallery Section */
.gallery {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--beige), var(--white));
    position: relative;
}

.gallery::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(33, 99, 84, 0.03), rgba(166, 108, 66, 0.03));
    pointer-events: none;
}

.gallery-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid var(--primary-brown);
    background: transparent;
    color: var(--primary-brown);
    border-radius: 25px;
    cursor: pointer;
    font-family: var(--font-body);
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(166, 108, 66, 0.1);
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-brown);
    color: var(--white);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(139, 69, 19, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay i {
    font-size: 2rem;
    color: var(--white);
}

/* Contact Section */
.contact {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--white), var(--cream));
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(166, 108, 66, 0.05), transparent 50%);
    pointer-events: none;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    color: var(--primary-brown);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: var(--gray);
}

.contact-item i {
    color: var(--primary-brown);
    width: 20px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-brown);
    color: var(--white);
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--olive-green);
    transform: translateY(-2px);
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    position: relative;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--warm-brown);
    font-size: 0.9rem;
    font-family: var(--font-arabic);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: var(--font-arabic);
    background: #fafafa;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--warm-brown);
    background: white;
    box-shadow: 0 0 0 3px rgba(166, 108, 66, 0.1);
    transform: translateY(-2px);
}

.form-group input:valid,
.form-group textarea:valid {
    border-color: var(--olive-green);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
    border-color: #e74c3c;
    background: #fdf2f2;
}

/* Error Messages */
.error-message {
    display: block;
    color: #e74c3c;
    font-size: 0.8rem;
    margin-top: 0.5rem;
    font-family: var(--font-arabic);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.error-message.show {
    opacity: 1;
    transform: translateY(0);
}

/* Character Counter */
.char-counter {
    text-align: right;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.5rem;
    font-family: var(--font-arabic);
}

.char-counter.warning {
    color: #f39c12;
}

.char-counter.danger {
    color: #e74c3c;
}

/* Checkbox Group */
.checkbox-group {
    margin: 1.5rem 0;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.5;
    font-family: var(--font-arabic);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    margin-left: 10px;
    margin-top: 2px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--warm-brown);
    border-color: var(--warm-brown);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.privacy-link {
    color: var(--warm-brown);
    text-decoration: underline;
    transition: color 0.3s ease;
}

.privacy-link:hover {
    color: var(--olive-green);
}

/* Submit Button Enhanced */
.contact-form .btn {
    position: relative;
    overflow: hidden;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Success Message */
.form-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
    text-align: center;
    font-family: var(--font-arabic);
    border: 1px solid #c3e6cb;
    animation: slideIn 0.5s ease;
}

.form-success i {
    margin-left: 0.5rem;
    color: #28a745;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--primary-brown), var(--deep-green));
    color: var(--white);
    padding: 3rem 0 1rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    pointer-events: none;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.footer-logo h3 {
    color: var(--white);
    font-family: var(--font-headings);
    margin: 0;
}

.footer-section h4 {
    color: var(--secondary-brown);
    margin-bottom: 1rem;
    font-family: var(--font-headings);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
    font-family: var(--font-body);
}

.footer-section ul li a:hover {
    color: var(--secondary-brown);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #444;
    color: #ccc;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
}

.modal-content {
    background-color: var(--white);
    margin: 5% auto;
    padding: 20px;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--black);
}

#mapContainer {
    height: 400px;
    width: 100%;
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Additional Responsive Styles for Other Sections */
@media (max-width: 768px) {
    .hero {
        padding: 100px 0 60px;
        min-height: 80vh;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
    }
    
    .menu-tabs {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    .tab-btn {
        width: 200px;
        margin: 0.25rem 0;
    }
    
    .menu-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .branches-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 15px;
    }
    
    #mapContainer {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .tab-btn {
        width: 180px;
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .menu-item {
        padding: 1rem;
    }
    
    .menu-item h3 {
        font-size: 1.1rem;
    }
    
    .menu-item .price {
        font-size: 1rem;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    .modal-content {
        width: 98%;
        margin: 5% auto;
        padding: 10px;
    }
    
    #mapContainer {
        height: 250px;
    }
    
    .pdf-menu-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .pdf-menu-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .pdf-embed-container {
        min-height: 400px;
    }
    
    .pdf-placeholder {
        height: 400px;
    }
}

@media (max-width: 480px) {
    /* Typography Mobile */
    .hero-title {
        font-size: 1.8rem;
        line-height: 1.3;
    }
    
    .hero-subtitle {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .section-title {
        font-size: 1.6rem;
        margin-bottom: 1.5rem;
    }
    
    /* Layout Mobile */
    .container {
        padding: 0 10px;
    }
    
    .hero {
        padding: 100px 0 40px;
        min-height: 90vh;
    }
    
    .hero-content {
        gap: 1.5rem;
        padding: 0 0.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 0.8rem;
    }
    
    .btn {
        width: 100%;
        padding: 0.9rem 1.2rem;
        font-size: 0.95rem;
    }
    
    /* Menu Mobile */
    .menu-search {
        padding: 0.8rem 0.8rem 0.8rem 2.5rem;
        font-size: 0.9rem;
    }
    
    .search-icon {
        right: 0.8rem;
        font-size: 1rem;
    }
    
    .price-filter {
        padding: 0.8rem;
        font-size: 0.9rem;
    }
    
    .clear-filters-btn {
        padding: 0.8rem;
        font-size: 0.9rem;
    }
    
    .menu-tabs {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }
    
    .tab-btn {
        width: auto;
        min-width: 120px;
        padding: 0.7rem 1rem;
        font-size: 0.85rem;
        margin: 0.2rem;
    }
    
    .menu-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .menu-item img {
        height: 180px;
        border-radius: 8px;
    }
    
    .menu-item h3 {
        font-size: 1rem;
        margin: 0.8rem 0 0.5rem;
    }
    
    .menu-item p {
        font-size: 0.85rem;
        line-height: 1.4;
        margin-bottom: 0.8rem;
    }
    
    .menu-item .price {
        font-size: 0.95rem;
        font-weight: 600;
    }
    
    /* Gallery Mobile */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .gallery-item {
        height: 250px;
    }
    
    /* Branches Mobile */
    .branch-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .branch-card h3 {
        font-size: 1.1rem;
    }
    
    .branch-info p {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }
    
    /* Contact Form Mobile */
    .contact-content {
        gap: 1.5rem;
    }
    
    .contact-form {
        padding: 1rem;
        margin: 0;
    }
    
    .form-label {
        font-size: 0.85rem;
        margin-bottom: 0.4rem;
    }
    
    .form-group {
        margin-bottom: 1.2rem;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 0.8rem;
        font-size: 16px;
        border-radius: 8px;
    }
    
    .checkbox-label {
        font-size: 0.8rem;
        line-height: 1.3;
    }
    
    .checkmark {
        width: 18px;
        height: 18px;
        margin-left: 8px;
    }
    
    /* Footer Mobile */
    .footer {
        padding: 2rem 0 1rem;
    }
    
    .footer-section h4 {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .footer-section ul li {
        margin-bottom: 0.4rem;
    }
    
    .footer-section ul li a {
        font-size: 0.9rem;
    }
    
    .social-links a {
        width: 35px;
        height: 35px;
        font-size: 1rem;
        margin: 0 0.3rem;
    }
    
    /* Modal Mobile */
    .modal-content {
        margin: 5% auto;
        width: 98%;
        padding: 1rem;
        border-radius: 12px;
    }
    
    #mapContainer {
        height: 200px;
        border-radius: 8px;
    }
    
    /* No Results Mobile */
    .no-results {
        padding: 2rem 1rem;
    }
    
    .no-results i {
        font-size: 2.5rem;
    }
    
    .no-results h3 {
        font-size: 1.1rem;
    }
    
    .no-results p {
        font-size: 0.9rem;
    }
}

.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

/* Mobile Navigation Fixes */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 0;
    }
    
    .nav-container {
        padding: 0 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: white;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        z-index: 999;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 1rem 0;
        width: 90%;
        text-align: center;
    }
    
    .nav-menu a {
        display: block;
        padding: 1rem;
        font-size: 1.1rem;
        border-bottom: 1px solid #f0f0f0;
        width: 100%;
    }
    
    .hamburger {
        display: flex;
        flex-direction: column;
        cursor: pointer;
        padding: 0.5rem;
    }
    
    .hamburger span {
        width: 25px;
        height: 3px;
        background: var(--warm-brown);
        margin: 3px 0;
        transition: 0.3s;
        border-radius: 2px;
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    .cta-button {
        display: none;
    }
    
    /* Menu Controls Mobile */
    .menu-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .search-container {
        max-width: none;
        order: 1;
    }
    
    .filter-container {
        order: 2;
        flex-direction: column;
        gap: 1rem;
    }
    
    .price-filter {
        width: 100%;
        min-width: auto;
    }
    
    .clear-filters-btn {
        width: 100%;
        justify-content: center;
    }
    
    /* Hero Section Mobile */
    .hero {
        padding: 120px 0 60px;
        min-height: 100vh;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
        padding: 0 1rem;
    }
    
    .hero-title {
        font-size: 2.2rem;
        line-height: 1.2;
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .hero-image img {
        height: 300px;
    }
    
    /* Form Mobile Fixes */
    .contact-form {
        padding: 1.5rem;
        margin: 0 1rem;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 1rem;
    }
    
    .checkbox-label {
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    /* Menu Items Mobile */
    .menu-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }
    
    .menu-item img {
        width: 100%;
        height: 200px;
        margin-bottom: 1rem;
    }
    
    .menu-item-content {
        width: 100%;
    }
    
    /* Gallery Mobile */
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    /* Footer Mobile */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .footer-section {
        padding: 1rem 0;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.6s ease-in-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Image Loading and Placeholder Styles */
img {
    transition: opacity 0.3s ease;
}

img.loading {
    opacity: 0.7;
}

img.loaded {
    opacity: 1;
}

img.placeholder-image {
    opacity: 0.8;
    filter: grayscale(20%);
}

/* Lazy loading animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.loading {
    animation: fadeIn 0.6s ease forwards;
}

/* Accessibility Improvements */
/* Focus styles for keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-brown);
    outline-offset: 2px;
    border-radius: 4px;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-brown);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 600;
}

.skip-link:focus {
    top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-brown: #8B4513;
        --deep-green: #1B5E20;
        --black: #000000;
        --white: #FFFFFF;
    }
}

/* Ensure sufficient color contrast */
.text-muted {
    color: #5A5A5A; /* Improved contrast */
}

/* Hover Effects */
.menu-item,
.feature-card,
.branch-card,
.gallery-item {
    transition: all 0.3s ease;
}

/* Focus indicators for interactive elements */
.menu-item:focus-within,
.feature-card:focus-within,
.branch-card:focus-within,
.gallery-item:focus-within {
    outline: 2px solid var(--primary-brown);
    outline-offset: 2px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-brown);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-brown);
}
