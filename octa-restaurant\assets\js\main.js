// ===== MAIN APPLICATION CLASS =====
class OctaRestaurant {
    constructor() {
        this.currentLang = document.documentElement.lang || 'en';
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.menuData = [];
        this.galleryData = [];
        this.activeMenuCategory = 'all';
        this.activeGalleryFilter = 'all';
        this.lightboxIndex = 0;
        
        this.init();
    }

    // ===== INITIALIZATION =====
    init() {
        this.loadData();
        this.setupEventListeners();
        this.setupIntersectionObserver();
        this.setupTheme();
        this.hideLoadingScreen();
        this.updateNavbarOnScroll();
    }

    // ===== DATA LOADING =====
    loadData() {
        this.loadMenuData();
        this.loadGalleryData();
    }

    loadMenuData() {
        this.menuData = [
            {
                id: 1,
                name: { en: 'Margherita Pizza', ar: 'بيتزا مارغريتا' },
                description: { 
                    en: 'Classic Italian pizza with fresh tomatoes, mozzarella, and basil',
                    ar: 'بيتزا إيطالية كلاسيكية مع الطماطم الطازجة والموتزاريلا والريحان'
                },
                price: 'EGP 180',
                image: 'assets/images/menu/pizza-margherita.jpg',
                category: 'pizza',
                tags: ['vegetarian', 'classic'],
                featured: true
            },
            {
                id: 2,
                name: { en: 'Pasta Carbonara', ar: 'باستا كاربونارا' },
                description: { 
                    en: 'Creamy pasta with pancetta, eggs, and parmesan cheese',
                    ar: 'معكرونة كريمية مع البانشيتا والبيض وجبن البارميزان'
                },
                price: 'EGP 220',
                image: 'assets/images/menu/pasta-carbonara.jpg',
                category: 'pasta',
                tags: ['creamy', 'traditional'],
                featured: false
            },
            {
                id: 3,
                name: { en: 'Bruschetta', ar: 'بروشيتا' },
                description: { 
                    en: 'Grilled bread topped with fresh tomatoes, garlic, and herbs',
                    ar: 'خبز مشوي مع الطماطم الطازجة والثوم والأعشاب'
                },
                price: 'EGP 85',
                image: 'assets/images/menu/bruschetta.jpg',
                category: 'appetizers',
                tags: ['fresh', 'light'],
                featured: false
            },
            {
                id: 4,
                name: { en: 'Tiramisu', ar: 'تيراميسو' },
                description: { 
                    en: 'Classic Italian dessert with coffee-soaked ladyfingers and mascarpone',
                    ar: 'حلوى إيطالية كلاسيكية مع البسكويت المنقوع بالقهوة والماسكاربوني'
                },
                price: 'EGP 120',
                image: 'assets/images/menu/tiramisu.jpg',
                category: 'desserts',
                tags: ['coffee', 'classic'],
                featured: true
            },
            {
                id: 5,
                name: { en: 'Osso Buco', ar: 'أوسو بوكو' },
                description: { 
                    en: 'Braised veal shanks with vegetables and white wine',
                    ar: 'لحم العجل المطبوخ مع الخضار والنبيذ الأبيض'
                },
                price: 'EGP 380',
                image: 'assets/images/menu/osso-buco.jpg',
                category: 'mains',
                tags: ['premium', 'traditional'],
                featured: true
            },
            {
                id: 6,
                name: { en: 'Espresso', ar: 'إسبريسو' },
                description: { 
                    en: 'Strong Italian coffee served in a small cup',
                    ar: 'قهوة إيطالية قوية تُقدم في كوب صغير'
                },
                price: 'EGP 45',
                image: 'assets/images/menu/espresso.jpg',
                category: 'beverages',
                tags: ['coffee', 'strong'],
                featured: false
            }
        ];
        
        this.renderMenuItems();
    }

    loadGalleryData() {
        this.galleryData = [
            {
                id: 1,
                src: 'assets/images/gallery/food-1.jpg',
                alt: { en: 'Delicious Italian Pizza', ar: 'بيتزا إيطالية شهية' },
                caption: { en: 'Authentic Italian Pizza', ar: 'بيتزا إيطالية أصيلة' },
                category: 'food'
            },
            {
                id: 2,
                src: 'assets/images/gallery/interior-1.jpg',
                alt: { en: 'Restaurant Interior', ar: 'ديكور المطعم' },
                caption: { en: 'Elegant Dining Atmosphere', ar: 'أجواء طعام أنيقة' },
                category: 'interior'
            },
            {
                id: 3,
                src: 'assets/images/gallery/food-2.jpg',
                alt: { en: 'Fresh Pasta Dish', ar: 'طبق معكرونة طازج' },
                caption: { en: 'Handmade Pasta', ar: 'معكرونة مصنوعة يدوياً' },
                category: 'food'
            },
            {
                id: 4,
                src: 'assets/images/gallery/events-1.jpg',
                alt: { en: 'Special Event', ar: 'فعالية خاصة' },
                caption: { en: 'Private Dining Experience', ar: 'تجربة طعام خاصة' },
                category: 'events'
            },
            {
                id: 5,
                src: 'assets/images/gallery/food-3.jpg',
                alt: { en: 'Italian Dessert', ar: 'حلوى إيطالية' },
                caption: { en: 'Traditional Tiramisu', ar: 'تيراميسو تقليدي' },
                category: 'food'
            },
            {
                id: 6,
                src: 'assets/images/gallery/interior-2.jpg',
                alt: { en: 'Cozy Seating Area', ar: 'منطقة جلوس مريحة' },
                caption: { en: 'Comfortable Dining Space', ar: 'مساحة طعام مريحة' },
                category: 'interior'
            }
        ];
        
        this.renderGalleryItems();
    }

    // ===== EVENT LISTENERS =====
    setupEventListeners() {
        // Navigation
        this.setupNavigationListeners();
        
        // Language toggle
        const langToggle = document.getElementById('langToggle');
        if (langToggle) {
            langToggle.addEventListener('click', () => this.toggleLanguage());
        }
        
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // Mobile menu
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const navMenu = document.getElementById('navMenu');
        if (mobileMenuToggle && navMenu) {
            mobileMenuToggle.addEventListener('click', () => {
                mobileMenuToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
        }
        
        // Menu categories
        this.setupMenuCategoryListeners();
        
        // Gallery filters
        this.setupGalleryFilterListeners();
        
        // Contact form
        this.setupContactFormListener();
        
        // Lightbox
        this.setupLightboxListeners();
        
        // Smooth scrolling
        this.setupSmoothScrolling();
        
        // Window events
        window.addEventListener('scroll', () => this.updateNavbarOnScroll());
        window.addEventListener('resize', () => this.handleResize());
    }

    setupNavigationListeners() {
        const navLinks = document.querySelectorAll('[data-scroll]');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('data-scroll');
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    this.scrollToElement(targetElement);
                }
            });
        });
    }

    setupMenuCategoryListeners() {
        const categoryBtns = document.querySelectorAll('.category-btn');
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.getAttribute('data-category');
                this.filterMenuItems(category);
                
                // Update active button
                categoryBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
    }

    setupGalleryFilterListeners() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.getAttribute('data-filter');
                this.filterGalleryItems(filter);
                
                // Update active button
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
    }

    setupContactFormListener() {
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleContactFormSubmit(e);
            });
        }
    }

    setupLightboxListeners() {
        const lightbox = document.getElementById('lightbox');
        const lightboxClose = document.getElementById('lightboxClose');
        const lightboxPrev = document.getElementById('lightboxPrev');
        const lightboxNext = document.getElementById('lightboxNext');
        
        if (lightboxClose) {
            lightboxClose.addEventListener('click', () => this.closeLightbox());
        }
        
        if (lightboxPrev) {
            lightboxPrev.addEventListener('click', () => this.previousLightboxImage());
        }
        
        if (lightboxNext) {
            lightboxNext.addEventListener('click', () => this.nextLightboxImage());
        }
        
        if (lightbox) {
            lightbox.addEventListener('click', (e) => {
                if (e.target === lightbox) {
                    this.closeLightbox();
                }
            });
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (lightbox && lightbox.classList.contains('active')) {
                switch (e.key) {
                    case 'Escape':
                        this.closeLightbox();
                        break;
                    case 'ArrowLeft':
                        this.previousLightboxImage();
                        break;
                    case 'ArrowRight':
                        this.nextLightboxImage();
                        break;
                }
            }
        });
    }

    setupSmoothScrolling() {
        // Smooth scrolling is handled by CSS scroll-behavior: smooth
        // This is just for additional smooth scrolling functionality
    }

    // ===== INTERSECTION OBSERVER =====
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                    
                    // Update active nav link
                    const id = entry.target.id;
                    if (id) {
                        this.updateActiveNavLink(id);
                    }
                }
            });
        }, observerOptions);

        // Observe all sections
        const sections = document.querySelectorAll('section[id]');
        sections.forEach(section => observer.observe(section));
        
        // Observe animated elements
        const animatedElements = document.querySelectorAll('.fade-in, .fade-in-up, .fade-in-left, .fade-in-right');
        animatedElements.forEach(element => observer.observe(element));
    }

    // ===== THEME MANAGEMENT =====
    setupTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        this.updateThemeIcon();
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);
        this.updateThemeIcon();
    }

    updateThemeIcon() {
        const themeIcon = document.getElementById('themeIcon');
        if (themeIcon) {
            themeIcon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // ===== LANGUAGE MANAGEMENT =====
    toggleLanguage() {
        const newLang = this.currentLang === 'en' ? 'ar' : 'en';
        const newUrl = newLang === 'ar' ? 'ar.html' : 'index.html';
        window.location.href = newUrl;
    }

    // ===== NAVIGATION =====
    updateNavbarOnScroll() {
        const navbar = document.getElementById('navbar');
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    }

    updateActiveNavLink(activeId) {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            const targetId = link.getAttribute('data-scroll');
            if (targetId === activeId) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    scrollToElement(element) {
        const navbarHeight = document.getElementById('navbar').offsetHeight;
        const elementPosition = element.offsetTop - navbarHeight - 20;
        
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }

    // ===== MENU FUNCTIONALITY =====
    renderMenuItems() {
        const menuGrid = document.getElementById('menuGrid');
        if (!menuGrid) return;

        menuGrid.innerHTML = '';
        
        this.menuData.forEach(item => {
            const menuItemElement = this.createMenuItemElement(item);
            menuGrid.appendChild(menuItemElement);
        });
    }

    createMenuItemElement(item) {
        const div = document.createElement('div');
        div.className = `menu-item ${item.featured ? 'featured' : ''}`;
        div.setAttribute('data-category', item.category);
        
        const name = item.name[this.currentLang];
        const description = item.description[this.currentLang];
        
        div.innerHTML = `
            <div class="menu-item-image">
                <img src="${item.image}" alt="${name}" loading="lazy">
                ${item.featured ? '<div class="menu-item-badge">Featured</div>' : ''}
            </div>
            <div class="menu-item-content">
                <div class="menu-item-header">
                    <h3 class="menu-item-title">${name}</h3>
                    <span class="menu-item-price">${item.price}</span>
                </div>
                <p class="menu-item-description">${description}</p>
                <div class="menu-item-tags">
                    ${item.tags.map(tag => `<span class="menu-tag">${tag}</span>`).join('')}
                </div>
                <div class="menu-item-actions">
                    <button class="btn btn-outline" data-branch="order">
                        <i class="fas fa-shopping-cart"></i>
                        <span>${this.currentLang === 'en' ? 'Order Now' : 'اطلب الآن'}</span>
                    </button>
                </div>
            </div>
        `;
        
        return div;
    }

    filterMenuItems(category) {
        this.activeMenuCategory = category;
        const menuItems = document.querySelectorAll('.menu-item');
        
        menuItems.forEach(item => {
            const itemCategory = item.getAttribute('data-category');
            if (category === 'all' || itemCategory === category) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });
    }

    // ===== GALLERY FUNCTIONALITY =====
    renderGalleryItems() {
        const galleryGrid = document.getElementById('galleryGrid');
        if (!galleryGrid) return;

        galleryGrid.innerHTML = '';
        
        this.galleryData.forEach((item, index) => {
            const galleryItemElement = this.createGalleryItemElement(item, index);
            galleryGrid.appendChild(galleryItemElement);
        });
    }

    createGalleryItemElement(item, index) {
        const div = document.createElement('div');
        div.className = 'gallery-item';
        div.setAttribute('data-filter', item.category);
        div.setAttribute('data-index', index);
        
        const alt = item.alt[this.currentLang];
        const caption = item.caption[this.currentLang];
        
        div.innerHTML = `
            <img src="${item.src}" alt="${alt}" loading="lazy">
            <div class="gallery-overlay">
                <h4>${caption}</h4>
                <button class="btn btn-outline">
                    <i class="fas fa-expand"></i>
                    <span>${this.currentLang === 'en' ? 'View' : 'عرض'}</span>
                </button>
            </div>
        `;
        
        div.addEventListener('click', () => this.openLightbox(index));
        
        return div;
    }

    filterGalleryItems(filter) {
        this.activeGalleryFilter = filter;
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            const itemFilter = item.getAttribute('data-filter');
            if (filter === 'all' || itemFilter === filter) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });
    }

    // ===== LIGHTBOX FUNCTIONALITY =====
    openLightbox(index) {
        this.lightboxIndex = index;
        const lightbox = document.getElementById('lightbox');
        const lightboxImage = document.getElementById('lightboxImage');
        const lightboxCaption = document.getElementById('lightboxCaption');
        
        if (lightbox && lightboxImage && lightboxCaption) {
            const item = this.galleryData[index];
            lightboxImage.src = item.src;
            lightboxImage.alt = item.alt[this.currentLang];
            lightboxCaption.textContent = item.caption[this.currentLang];
            
            lightbox.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeLightbox() {
        const lightbox = document.getElementById('lightbox');
        if (lightbox) {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    previousLightboxImage() {
        this.lightboxIndex = this.lightboxIndex > 0 ? this.lightboxIndex - 1 : this.galleryData.length - 1;
        this.updateLightboxImage();
    }

    nextLightboxImage() {
        this.lightboxIndex = this.lightboxIndex < this.galleryData.length - 1 ? this.lightboxIndex + 1 : 0;
        this.updateLightboxImage();
    }

    updateLightboxImage() {
        const lightboxImage = document.getElementById('lightboxImage');
        const lightboxCaption = document.getElementById('lightboxCaption');
        
        if (lightboxImage && lightboxCaption) {
            const item = this.galleryData[this.lightboxIndex];
            lightboxImage.src = item.src;
            lightboxImage.alt = item.alt[this.currentLang];
            lightboxCaption.textContent = item.caption[this.currentLang];
        }
    }

    // ===== CONTACT FORM =====
    handleContactFormSubmit(e) {
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        
        // Here you would typically send the data to a server
        console.log('Contact form submitted:', data);
        
        // Show success message
        this.showNotification(
            this.currentLang === 'en' 
                ? 'Thank you for your message! We will get back to you soon.' 
                : 'شكراً لرسالتك! سنتواصل معك قريباً.',
            'success'
        );
        
        // Reset form
        e.target.reset();
    }

    // ===== UTILITY FUNCTIONS =====
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 5000);
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 1500);
        }
    }

    handleResize() {
        // Handle any resize-specific functionality
        this.updateNavbarOnScroll();
    }
}

// ===== INITIALIZE APPLICATION =====
document.addEventListener('DOMContentLoaded', () => {
    new OctaRestaurant();
});

// ===== ADDITIONAL UTILITY FUNCTIONS =====
// Preload critical images
function preloadImages() {
    const criticalImages = [
        'assets/images/logo.png',
        'assets/images/hero-dish.jpg',
        'assets/images/hero-bg.jpg'
    ];
    
    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Call preload function
preloadImages();
