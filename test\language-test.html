<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Test - OCTA Restaurant</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        :root {
            --primary-brown: #A66C42;
            --deep-green: #216354;
            --primary-hover: #8B5A35;
            --accent-gold: #D4AF37;
            --warm-cream: #FDF6E3;
            --elegant-shadow: rgba(166, 108, 66, 0.12);
            --elegant-border: rgba(166, 108, 66, 0.15);
            --white: #FFFFFF;
            --black: #1A1A1A;
            --light-gray: #F8F8F8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--warm-cream);
            color: var(--black);
            line-height: 1.6;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        [data-lang="ar"] {
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif;
        }

        [data-lang="en"] {
            direction: ltr;
            text-align: left;
            font-family: 'Inter', sans-serif;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 8px 25px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }

        .header h1 {
            color: var(--primary-brown);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .header h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--accent-gold);
            border-radius: 2px;
        }

        .lang-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .lang-btn {
            background: var(--primary-brown);
            color: var(--white);
            border: 2px solid var(--primary-brown);
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .lang-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-hover);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .lang-btn:hover::before {
            left: 0;
        }

        .lang-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--elegant-shadow);
        }

        .lang-btn.active {
            background: var(--deep-green);
            border-color: var(--deep-green);
        }

        .test-section {
            background: var(--white);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }

        .test-section h2 {
            color: var(--primary-brown);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .content-card {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid var(--elegant-border);
        }

        .content-card h3 {
            color: var(--deep-green);
            margin-bottom: 1rem;
        }

        .menu-item {
            background: var(--white);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }

        .menu-item h4 {
            color: var(--primary-brown);
            margin-bottom: 0.5rem;
        }

        .menu-item .price {
            color: var(--deep-green);
            font-weight: bold;
            font-size: 1.1rem;
        }

        .status-indicator {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            margin: 0.5rem;
        }

        .status-en {
            background: rgba(33, 99, 84, 0.1);
            color: var(--deep-green);
            border: 2px solid var(--deep-green);
        }

        .status-ar {
            background: rgba(166, 108, 66, 0.1);
            color: var(--primary-brown);
            border: 2px solid var(--primary-brown);
        }

        .direction-test {
            border: 2px dashed var(--elegant-border);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 10px;
        }

        .rtl-test {
            background: linear-gradient(to right, var(--primary-brown), transparent);
            height: 4px;
            margin: 0.5rem 0;
        }

        .ltr-test {
            background: linear-gradient(to left, var(--deep-green), transparent);
            height: 4px;
            margin: 0.5rem 0;
        }

        [data-lang="ar"] .rtl-test {
            background: linear-gradient(to left, var(--primary-brown), transparent);
        }

        [data-lang="ar"] .ltr-test {
            background: linear-gradient(to right, var(--deep-green), transparent);
        }
    </style>
</head>
<body data-lang="en">
    <div class="container">
        <div class="header">
            <h1 id="main-title">🌐 Language System Test</h1>
            <p id="main-description">Testing the bilingual functionality of OCTA Restaurant</p>
        </div>

        <div class="lang-controls">
            <button class="lang-btn active" onclick="setLanguage('en')" id="en-btn">
                <i class="fas fa-flag-usa"></i> English
            </button>
            <button class="lang-btn" onclick="setLanguage('ar')" id="ar-btn">
                <i class="fas fa-flag"></i> العربية
            </button>
        </div>

        <div class="test-section">
            <h2 id="status-title">Current Status</h2>
            <div>
                <span class="status-indicator status-en" id="en-status">English: Active</span>
                <span class="status-indicator status-ar" id="ar-status">Arabic: Inactive</span>
            </div>
            <div class="direction-test">
                <p id="direction-text">Direction Test: This text should align based on language</p>
                <div class="rtl-test"></div>
                <div class="ltr-test"></div>
            </div>
        </div>

        <div class="test-section">
            <h2 id="content-title">Content Test</h2>
            <div class="content-grid">
                <div class="content-card">
                    <h3 id="restaurant-title">Restaurant Information</h3>
                    <p id="restaurant-desc">OCTA Restaurant offers authentic Italian cuisine in the heart of Egypt. Our experienced chefs prepare traditional dishes with modern presentation.</p>
                </div>
                <div class="content-card">
                    <h3 id="location-title">Our Locations</h3>
                    <p id="location-desc">Visit us in Cairo (New Cairo, 5th Settlement) or Alexandria (Sidi Gaber). Both locations offer the same high-quality dining experience.</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 id="menu-title">Menu Items Test</h2>
            <div id="menu-items">
                <div class="menu-item">
                    <h4 id="item1-name">Margherita Pizza</h4>
                    <p id="item1-desc">Classic Italian pizza with fresh tomatoes, mozzarella, and basil</p>
                    <div class="price" id="item1-price">120 EGP</div>
                </div>
                <div class="menu-item">
                    <h4 id="item2-name">Pasta Carbonara</h4>
                    <p id="item2-desc">Creamy Italian pasta with bacon, eggs, and Parmesan cheese</p>
                    <div class="price" id="item2-price">95 EGP</div>
                </div>
                <div class="menu-item">
                    <h4 id="item3-name">Tiramisu</h4>
                    <p id="item3-desc">Classic Italian dessert with coffee and mascarpone</p>
                    <div class="price" id="item3-price">65 EGP</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 id="features-title">Features Test</h2>
            <ul id="features-list">
                <li id="feature1">✅ Bilingual support (English/Arabic)</li>
                <li id="feature2">✅ RTL/LTR text direction</li>
                <li id="feature3">✅ Dynamic content translation</li>
                <li id="feature4">✅ Persistent language preference</li>
                <li id="feature5">✅ Responsive design for both languages</li>
            </ul>
        </div>
    </div>

    <script>
        // Language content data
        const content = {
            en: {
                'main-title': '🌐 Language System Test',
                'main-description': 'Testing the bilingual functionality of OCTA Restaurant',
                'status-title': 'Current Status',
                'en-status': 'English: Active',
                'ar-status': 'Arabic: Inactive',
                'direction-text': 'Direction Test: This text should align based on language',
                'content-title': 'Content Test',
                'restaurant-title': 'Restaurant Information',
                'restaurant-desc': 'OCTA Restaurant offers authentic Italian cuisine in the heart of Egypt. Our experienced chefs prepare traditional dishes with modern presentation.',
                'location-title': 'Our Locations',
                'location-desc': 'Visit us in Cairo (New Cairo, 5th Settlement) or Alexandria (Sidi Gaber). Both locations offer the same high-quality dining experience.',
                'menu-title': 'Menu Items Test',
                'item1-name': 'Margherita Pizza',
                'item1-desc': 'Classic Italian pizza with fresh tomatoes, mozzarella, and basil',
                'item1-price': '120 EGP',
                'item2-name': 'Pasta Carbonara',
                'item2-desc': 'Creamy Italian pasta with bacon, eggs, and Parmesan cheese',
                'item2-price': '95 EGP',
                'item3-name': 'Tiramisu',
                'item3-desc': 'Classic Italian dessert with coffee and mascarpone',
                'item3-price': '65 EGP',
                'features-title': 'Features Test',
                'feature1': '✅ Bilingual support (English/Arabic)',
                'feature2': '✅ RTL/LTR text direction',
                'feature3': '✅ Dynamic content translation',
                'feature4': '✅ Persistent language preference',
                'feature5': '✅ Responsive design for both languages'
            },
            ar: {
                'main-title': '🌐 اختبار نظام اللغات',
                'main-description': 'اختبار الوظائف ثنائية اللغة لمطعم أوكتا',
                'status-title': 'الحالة الحالية',
                'en-status': 'الإنجليزية: غير نشطة',
                'ar-status': 'العربية: نشطة',
                'direction-text': 'اختبار الاتجاه: هذا النص يجب أن يتماشى مع اللغة',
                'content-title': 'اختبار المحتوى',
                'restaurant-title': 'معلومات المطعم',
                'restaurant-desc': 'مطعم أوكتا يقدم المأكولات الإيطالية الأصيلة في قلب مصر. طهاتنا ذوو الخبرة يحضرون الأطباق التقليدية بعرض عصري.',
                'location-title': 'مواقعنا',
                'location-desc': 'زورونا في القاهرة (القاهرة الجديدة، التجمع الخامس) أو الإسكندرية (سيدي جابر). كلا الموقعين يقدمان نفس تجربة الطعام عالية الجودة.',
                'menu-title': 'اختبار عناصر القائمة',
                'item1-name': 'بيتزا مارغريتا',
                'item1-desc': 'بيتزا إيطالية كلاسيكية مع الطماطم الطازجة والموزاريلا والريحان',
                'item1-price': '120 جنيه',
                'item2-name': 'باستا كاربونارا',
                'item2-desc': 'معكرونة إيطالية كريمية مع البيكون والبيض والجبن البارميزان',
                'item2-price': '95 جنيه',
                'item3-name': 'تيراميسو',
                'item3-desc': 'حلوى إيطالية كلاسيكية مع القهوة والماسكاربوني',
                'item3-price': '65 جنيه',
                'features-title': 'اختبار الميزات',
                'feature1': '✅ دعم ثنائي اللغة (الإنجليزية/العربية)',
                'feature2': '✅ اتجاه النص من اليمين لليسار/من اليسار لليمين',
                'feature3': '✅ ترجمة المحتوى الديناميكي',
                'feature4': '✅ تفضيل اللغة المستمر',
                'feature5': '✅ تصميم متجاوب لكلا اللغتين'
            }
        };

        function setLanguage(lang) {
            // Update body attributes
            document.body.setAttribute('data-lang', lang);
            document.documentElement.setAttribute('lang', lang);
            document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');

            // Update button states
            document.getElementById('en-btn').classList.toggle('active', lang === 'en');
            document.getElementById('ar-btn').classList.toggle('active', lang === 'ar');

            // Update all content
            const langContent = content[lang];
            for (const [id, text] of Object.entries(langContent)) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = text;
                }
            }

            // Update status indicators
            if (lang === 'en') {
                document.getElementById('en-status').className = 'status-indicator status-en';
                document.getElementById('ar-status').className = 'status-indicator status-ar';
            } else {
                document.getElementById('en-status').className = 'status-indicator status-ar';
                document.getElementById('ar-status').className = 'status-indicator status-en';
            }

            // Save preference
            localStorage.setItem('test-language', lang);

            console.log(`Language switched to: ${lang}`);
        }

        // Initialize with saved language or default to English
        document.addEventListener('DOMContentLoaded', () => {
            const savedLang = localStorage.getItem('test-language') || 'en';
            setLanguage(savedLang);
        });
    </script>
</body>
</html>
