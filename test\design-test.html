<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم الجديد - أوكتا</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        :root {
            --primary-brown: #A66C42;
            --deep-green: #216354;
            --primary-hover: #8B5A35;
            --green-hover: #1A4F42;
            --accent-gold: #D4AF37;
            --warm-cream: #FDF6E3;
            --elegant-shadow: rgba(166, 108, 66, 0.12);
            --elegant-border: rgba(166, 108, 66, 0.15);
            --soft-glow: rgba(166, 108, 66, 0.25);
            --white: #FFFFFF;
            --black: #1A1A1A;
            --light-gray: #F8F8F8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--warm-cream);
            color: var(--black);
            line-height: 1.6;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 8px 25px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }

        .header h1 {
            color: var(--primary-brown);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .header h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--accent-gold);
            border-radius: 2px;
        }

        .test-section {
            background: var(--white);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px var(--elegant-shadow);
            border: 1px solid var(--elegant-border);
        }

        .test-section h2 {
            color: var(--primary-brown);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        /* Button Tests */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border: 2px solid;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-brown);
            color: var(--white);
            border-color: var(--primary-brown);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-hover);
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: -1;
        }

        .btn-primary:hover::before {
            left: 0;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--elegant-shadow);
        }

        .btn-secondary {
            background: transparent;
            color: var(--deep-green);
            border-color: var(--deep-green);
        }

        .btn-secondary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--deep-green);
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: -1;
        }

        .btn-secondary:hover::before {
            left: 0;
        }

        .btn-secondary:hover {
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(33, 99, 84, 0.15);
        }

        /* Icon Tests */
        .icon-test {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin: 1rem 0;
        }

        .icon {
            width: 60px;
            height: 60px;
            background: var(--primary-brown);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.5rem;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px var(--elegant-shadow);
        }

        .icon::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: var(--accent-gold);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .icon:hover::before {
            opacity: 1;
        }

        .icon:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--elegant-shadow);
        }

        /* Card Tests */
        .card {
            background: var(--white);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px var(--elegant-shadow);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--elegant-border);
            position: relative;
            margin: 1rem 0;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-brown);
            transform: scaleX(0);
            transition: transform 0.3s ease;
            transform-origin: left;
            z-index: 1;
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px var(--elegant-shadow);
            border-color: var(--soft-glow);
        }

        .card-content {
            padding: 1.5rem;
        }

        /* Form Tests */
        .form-group {
            margin: 1rem 0;
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--elegant-border);
            border-radius: 10px;
            font-size: 1rem;
            background: var(--white);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-brown);
            box-shadow: 0 0 0 3px var(--elegant-shadow);
            transform: translateY(-1px);
        }

        .form-input:hover {
            border-color: var(--soft-glow);
        }

        /* Social Links */
        .social-link {
            width: 50px;
            height: 50px;
            background: var(--light-gray);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin: 0.5rem;
        }

        .social-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-brown);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .social-link:hover::before {
            left: 0;
        }

        .social-link:hover {
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px var(--elegant-shadow);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 التصميم الجديد لأوكتا</h1>
            <p>تصميم أنيق وعصري بدون تدرجات مزعجة</p>
        </div>

        <div class="test-section">
            <h2>🔘 اختبار الأزرار</h2>
            <div>
                <button class="btn btn-primary">
                    <i class="fas fa-utensils"></i>
                    زر أساسي
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-heart"></i>
                    زر ثانوي
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-phone"></i>
                    اتصل بنا
                </button>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 اختبار الأيقونات</h2>
            <div class="icon-test">
                <div class="icon"><i class="fas fa-pizza-slice"></i></div>
                <div class="icon"><i class="fas fa-coffee"></i></div>
                <div class="icon"><i class="fas fa-wine-glass"></i></div>
                <div class="icon"><i class="fas fa-utensils"></i></div>
                <div class="icon"><i class="fas fa-heart"></i></div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 اختبار الكروت</h2>
            <div class="grid">
                <div class="card">
                    <div class="card-content">
                        <h3>بيتزا مارغريتا</h3>
                        <p>بيتزا إيطالية كلاسيكية مع الطماطم الطازجة والموزاريلا والريحان</p>
                        <strong>120 جنيه</strong>
                    </div>
                </div>
                <div class="card">
                    <div class="card-content">
                        <h3>باستا كاربونارا</h3>
                        <p>معكرونة إيطالية كريمية مع البيكون والبيض والجبن البارميزان</p>
                        <strong>95 جنيه</strong>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📝 اختبار النماذج</h2>
            <div class="grid">
                <div>
                    <div class="form-group">
                        <input type="text" class="form-input" placeholder="الاسم الكامل">
                    </div>
                    <div class="form-group">
                        <input type="email" class="form-input" placeholder="البريد الإلكتروني">
                    </div>
                    <div class="form-group">
                        <textarea class="form-input" placeholder="رسالتك" rows="4"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 اختبار الروابط الاجتماعية</h2>
            <div>
                <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                <a href="#" class="social-link"><i class="fab fa-whatsapp"></i></a>
            </div>
        </div>

        <div class="test-section">
            <h2>✨ الميزات الجديدة</h2>
            <ul style="text-align: right; padding-right: 2rem;">
                <li>✅ إزالة التدرجات المزعجة</li>
                <li>✅ تأثيرات انتقال سلسة</li>
                <li>✅ ألوان أنيقة ومتناسقة</li>
                <li>✅ تأثيرات hover احترافية</li>
                <li>✅ ظلال ناعمة وأنيقة</li>
                <li>✅ حدود رقيقة وجميلة</li>
                <li>✅ تصميم عصري ومتطور</li>
            </ul>
        </div>
    </div>
</body>
</html>
