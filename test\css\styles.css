/* ===================================
   OCTA RESTAURANT - MODERN CSS DESIGN
   Created with love for Italian cuisine
   =================================== */

/* CSS Custom Properties - Octa Brand Colors */
:root {
    /* Brand Colors - Following Octa Requirements */
    --primary-brown: #A66C42;
    --secondary-brown: #A4693E;
    --tertiary-brown: #A56A40;
    --deep-green: #216354;
    --white: #FFFFFF;
    --black: #1A1A1A;
    --gray: #666666;
    --light-gray: #F8F8F8;
    --cream: #FAF7F2;
    --beige: #F5F1EB;
    
    /* Enhanced Color Palette */
    --brown-light: rgba(166, 108, 66, 0.1);
    --brown-medium: rgba(166, 108, 66, 0.2);
    --brown-dark: rgba(166, 108, 66, 0.8);
    --green-light: rgba(33, 99, 84, 0.1);
    --green-medium: rgba(33, 99, 84, 0.2);
    --green-dark: rgba(33, 99, 84, 0.8);
    
    /* Modern Design System */
    --primary-hover: #8B5A35;
    --green-hover: #1A4F42;
    --accent-gold: #D4AF37;
    --warm-cream: #FDF6E3;
    --elegant-shadow: rgba(166, 108, 66, 0.12);
    --elegant-border: rgba(166, 108, 66, 0.15);
    --soft-glow: rgba(166, 108, 66, 0.25);
    
    /* Typography */
    --font-primary: 'Playfair Display', serif;
    --font-secondary: 'Inter', sans-serif;
    --font-arabic: 'Cairo', sans-serif;
    
    /* Font Weights */
    --fw-light: 300;
    --fw-regular: 400;
    --fw-medium: 500;
    --fw-semibold: 600;
    --fw-bold: 700;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 50%;
    
    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --white: #1A1A1A;
    --black: #FFFFFF;
    --light-gray: #2A2A2A;
    --cream: #1F1F1F;
    --beige: #252525;
    --gray: #B0B0B0;

    /* Dark theme specific adjustments */
    --brown-light: rgba(166, 108, 66, 0.15);
    --brown-medium: rgba(166, 108, 66, 0.25);
    --green-light: rgba(33, 99, 84, 0.15);
    --green-medium: rgba(33, 99, 84, 0.25);

    /* Enhanced shadows for dark mode */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.6);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.7);
}

/* Dark theme specific styles */
[data-theme="dark"] .navbar {
    background: rgba(26, 26, 26, 0.95);
    border-bottom-color: rgba(166, 108, 66, 0.2);
}

[data-theme="dark"] .navbar.scrolled {
    background: rgba(26, 26, 26, 0.98);
}

[data-theme="dark"] .hero {
    background: linear-gradient(135deg, var(--cream) 0%, var(--beige) 100%);
}

[data-theme="dark"] .about {
    background: var(--white);
}

[data-theme="dark"] .menu,
[data-theme="dark"] .branches {
    background: var(--beige);
}

[data-theme="dark"] .gallery,
[data-theme="dark"] .contact {
    background: var(--white);
}

[data-theme="dark"] .menu-item,
[data-theme="dark"] .branch-card,
[data-theme="dark"] .contact-form {
    background: var(--light-gray);
    border: 1px solid rgba(166, 108, 66, 0.15);
}

[data-theme="dark"] .form-input,
[data-theme="dark"] .search-input,
[data-theme="dark"] .newsletter-input {
    background: var(--light-gray);
    border-color: rgba(166, 108, 66, 0.3);
    color: var(--black);
}

[data-theme="dark"] .form-input:focus,
[data-theme="dark"] .search-input:focus {
    background: var(--light-gray);
    border-color: var(--primary-brown);
}

[data-theme="dark"] .lightbox {
    background: rgba(0, 0, 0, 0.95);
}

[data-theme="dark"] .footer {
    background: var(--beige);
}

[data-theme="dark"] .loading-screen {
    background: linear-gradient(135deg, var(--cream) 0%, var(--beige) 100%);
}

/* Dark theme button styles */
[data-theme="dark"] .filter-btn {
    background: var(--light-gray);
    color: var(--black);
    border-color: rgba(166, 108, 66, 0.2);
}

[data-theme="dark"] .filter-btn:hover,
[data-theme="dark"] .filter-btn.active {
    background: var(--primary-brown);
    color: var(--white);
}

/* Dark theme text colors */
[data-theme="dark"] .section-subtitle {
    color: var(--primary-brown);
}

[data-theme="dark"] .section-title {
    color: var(--black);
}

[data-theme="dark"] .section-description {
    color: var(--gray);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    overflow-x: hidden;
    /* Custom viewport height for mobile */
    --vh: 1vh;
}

body {
    font-family: var(--font-secondary);
    font-weight: var(--fw-regular);
    line-height: 1.7;
    color: var(--black);
    background-color: var(--white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Enhanced Typography for Languages */
[data-lang="en"] body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: 0.01em;
    font-size: 1rem;
}

[data-lang="ar"] body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
    letter-spacing: 0.02em;
    font-size: 1rem;
}

/* Improved heading typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: var(--fw-semibold);
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--black);
}

[data-lang="en"] h1, [data-lang="en"] h2, [data-lang="en"] h3,
[data-lang="en"] h4, [data-lang="en"] h5, [data-lang="en"] h6 {
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -0.02em;
    font-weight: var(--fw-medium);
}

[data-lang="ar"] h1, [data-lang="ar"] h2, [data-lang="ar"] h3,
[data-lang="ar"] h4, [data-lang="ar"] h5, [data-lang="ar"] h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-bold);
}

/* Better paragraph spacing */
p {
    margin-bottom: 1.2em;
    line-height: 1.7;
}

[data-lang="en"] p {
    line-height: 1.6;
    font-size: 1rem;
}

[data-lang="ar"] p {
    line-height: 1.8;
    font-size: 1rem;
}

/* Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-brown);
    color: var(--white);
    padding: 8px 16px;
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: var(--z-tooltip);
    font-weight: var(--fw-semibold);
    transition: var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: var(--fw-semibold);
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--black);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: var(--fw-bold);
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--gray);
}

/* Arabic Text */
[lang="ar"], .arabic-text {
    font-family: var(--font-arabic);
    direction: rtl;
    text-align: right;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    width: 100%;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-brown);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-tooltip);
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: var(--white);
}

.loading-logo img {
    width: 80px;
    height: 80px;
    margin-bottom: var(--spacing-md);
    animation: pulse 2s infinite;
}

.loading-spinner {
    margin: var(--spacing-md) 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--white);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

.loading-text {
    font-family: var(--font-arabic);
    font-size: 1.1rem;
    font-weight: var(--fw-medium);
    margin-top: var(--spacing-md);
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(166, 108, 66, 0.3); }
    50% { box-shadow: 0 0 30px rgba(166, 108, 66, 0.6); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.align-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.fade-in { animation: fadeInUp 0.6s ease forwards; }
.fade-in-left { animation: fadeInLeft 0.6s ease forwards; }
.fade-in-right { animation: fadeInRight 0.6s ease forwards; }

/* ===================================
   NAVIGATION STYLES
   =================================== */

.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(166, 108, 66, 0.1);
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
    height: 70px;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    position: relative;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    color: var(--black);
}

.logo-image {
    width: 45px;
    height: 45px;
    object-fit: contain;
    border-radius: var(--radius-md);
}

.logo-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: var(--fw-bold);
    color: var(--primary-brown);
}

/* Enhanced logo typography */
[data-lang="en"] .logo-text {
    font-family: 'Playfair Display', Georgia, serif;
    font-weight: var(--fw-bold);
    letter-spacing: -0.01em;
}

[data-lang="ar"] .logo-text {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-bold);
}

.nav-menu {
    display: flex;
    align-items: center;
    list-style: none;
    gap: var(--spacing-md);
    margin: 0;
    padding: 0;
}

/* Adjust spacing for English version */
[data-lang="en"] .nav-menu {
    gap: var(--spacing-sm);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--black);
    font-weight: var(--fw-medium);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    position: relative;
    font-size: 0.95rem;
}

/* Enhanced nav link typography */
[data-lang="en"] .nav-link {
    font-family: 'Inter', sans-serif;
    font-weight: var(--fw-medium);
    letter-spacing: 0.01em;
    font-size: 0.95rem;
}

[data-lang="ar"] .nav-link {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-semibold);
    font-size: 0.9rem;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-brown);
    transition: var(--transition-normal);
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 100%;
}

.nav-link:hover {
    color: var(--primary-brown);
    background: var(--brown-light);
}

.nav-link i {
    font-size: 0.9rem;
}

/* Language & Theme Controls */
.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Hide controls on mobile to prevent overlap */
@media (max-width: 768px) {
    .nav-controls {
        gap: var(--spacing-xs);
    }

    .lang-toggle span {
        display: none;
    }

    .lang-toggle {
        width: 40px;
        height: 40px;
        padding: 0;
        justify-content: center;
    }
}

/* Language Toggle */
.lang-toggle {
    background: none;
    border: 2px solid var(--elegant-border);
    color: var(--black);
    font-size: 0.9rem;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: var(--fw-medium);
    position: relative;
    overflow: hidden;
}

.lang-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-brown);
    transition: left 0.3s ease;
    z-index: -1;
}

.lang-toggle:hover::before {
    left: 0;
}

.lang-toggle:hover {
    color: var(--white);
    border-color: var(--primary-brown);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--elegant-shadow);
}

.lang-toggle i {
    font-size: 1rem;
}

[data-theme="dark"] .lang-toggle {
    border-color: rgba(166, 108, 66, 0.3);
    color: var(--black);
}

[data-theme="dark"] .lang-toggle:hover {
    color: var(--white);
}

/* Navigation CTA - Removed for better navbar layout */

/* Theme Toggle */
.theme-toggle {
    background: none;
    border: none;
    color: var(--black);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--brown-light);
    color: var(--primary-brown);
    transform: scale(1.1);
}

.theme-toggle:focus {
    outline: 2px solid var(--primary-brown);
    outline-offset: 2px;
}

/* Theme toggle animation */
.theme-toggle i {
    transition: var(--transition-normal);
}

[data-theme="dark"] .theme-toggle {
    color: var(--black);
}

[data-theme="dark"] .theme-toggle:hover {
    background: var(--brown-light);
    color: var(--primary-brown);
}

/* Mobile Menu Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    gap: 4px;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: var(--primary-brown);
    border-radius: 2px;
    transition: var(--transition-normal);
}

/* Dark mode for hamburger lines */
[data-theme="dark"] .hamburger-line {
    background: var(--primary-brown);
}

.nav-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.nav-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* CTA Button */
.cta-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--primary-brown);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-xl);
    font-weight: var(--fw-semibold);
    font-size: 1rem;
    transition: var(--transition-normal);
    border: 2px solid var(--primary-brown);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    line-height: 1.2;
}

/* Better typography for CTA buttons */
[data-lang="en"] .cta-btn {
    font-family: 'Inter', sans-serif;
    font-weight: var(--fw-semibold);
    letter-spacing: 0.01em;
}

[data-lang="ar"] .cta-btn {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-semibold);
}

.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-hover);
    transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
}

.cta-btn:hover::before {
    left: 0;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px var(--elegant-shadow);
}

.cta-btn:active {
    transform: translateY(-1px);
}

/* Mobile CTA removed */

/* ===================================
   HERO SECTION STYLES
   =================================== */

.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--warm-cream);
    padding-top: 70px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23A66C42" opacity="0.3"><animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="%23216354" opacity="0.4"><animate attributeName="opacity" values="0.4;0.9;0.4" dur="4s" repeatCount="indefinite"/></circle><circle cx="60" cy="70" r="2.5" fill="%23A4693E" opacity="0.2"><animate attributeName="opacity" values="0.2;0.7;0.2" dur="5s" repeatCount="indefinite"/></circle></svg>') repeat;
    animation: float 6s ease-in-out infinite;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(166, 108, 66, 0.03);
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.hero-text {
    animation: fadeInLeft 1s ease forwards;
}

.hero-title {
    margin-bottom: var(--spacing-lg);
}

.title-line {
    display: block;
    font-size: 1.2rem;
    font-weight: var(--fw-regular);
    color: var(--gray);
    margin-bottom: var(--spacing-xs);
}

.title-main {
    display: block;
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: var(--fw-bold);
    color: var(--primary-brown);
    margin-bottom: var(--spacing-xs);
    position: relative;
    text-shadow: 2px 2px 4px rgba(166, 108, 66, 0.1);
    line-height: 1.1;
}

.title-main::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: var(--accent-gold);
    border-radius: 2px;
}

/* Enhanced hero title typography */
[data-lang="en"] .title-main {
    font-family: 'Playfair Display', Georgia, serif;
    font-weight: var(--fw-bold);
    letter-spacing: -0.03em;
    line-height: 1.0;
}

[data-lang="ar"] .title-main {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-bold);
    line-height: 1.2;
}

[data-lang="en"] .title-main::after {
    left: 0;
}

[data-lang="ar"] .title-main::after {
    right: 0;
    left: auto;
}

.title-subtitle {
    display: block;
    font-size: 1.5rem;
    font-weight: var(--fw-medium);
    color: var(--deep-green);
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--gray);
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: var(--fw-semibold);
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 1rem;
}

/* Enhanced button typography */
[data-lang="en"] .btn {
    font-family: 'Inter', sans-serif;
    font-weight: var(--fw-semibold);
    letter-spacing: 0.01em;
    font-size: 0.95rem;
}

[data-lang="ar"] .btn {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-semibold);
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--primary-brown);
    color: var(--white);
    border: 2px solid var(--primary-brown);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-hover);
    transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
}

.btn-primary:hover::before {
    left: 0;
}

.btn-primary:hover {
    border-color: var(--primary-hover);
    box-shadow: 0 8px 25px var(--elegant-shadow);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-brown);
    border: 2px solid var(--primary-brown);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-brown);
    transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
}

.btn-secondary:hover::before {
    left: 0;
}

.btn-secondary:hover {
    color: var(--white);
    box-shadow: 0 8px 25px var(--elegant-shadow);
}

.btn-outline {
    background: transparent;
    color: var(--deep-green);
    border: 2px solid var(--deep-green);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-outline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--deep-green);
    transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
}

.btn-outline:hover::before {
    left: 0;
}

.btn-outline:hover {
    color: var(--white);
    border-color: var(--green-hover);
    box-shadow: 0 8px 25px rgba(33, 99, 84, 0.15);
}

.btn:hover {
    transform: translateY(-2px);
}

.hero-image {
    position: relative;
    animation: fadeInRight 1s ease forwards;
}

.hero-image-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.hero-dish {
    width: 100%;
    height: auto;
    display: block;
    transition: var(--transition-slow);
}

.hero-image-decoration {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: var(--accent-gold);
    border-radius: var(--radius-full);
    opacity: 0.8;
    animation: float 4s ease-in-out infinite;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--gray);
    animation: fadeInUp 1.5s ease forwards;
}

.scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid var(--primary-brown);
    border-radius: 12px;
    margin: 0 auto var(--spacing-xs);
    position: relative;
}

.scroll-wheel {
    width: 4px;
    height: 8px;
    background: var(--primary-brown);
    border-radius: 2px;
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    animation: scroll-wheel 2s infinite;
}

.scroll-text {
    font-size: 0.9rem;
    font-weight: var(--fw-medium);
}

@keyframes scroll-wheel {
    0% { top: 6px; opacity: 1; }
    100% { top: 20px; opacity: 0; }
}

/* ===================================
   SECTION STYLES
   =================================== */

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
    animation: fadeInUp 0.8s ease forwards;
}

.section-subtitle {
    display: inline-block;
    font-size: 0.9rem;
    font-weight: var(--fw-semibold);
    color: var(--primary-brown);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: var(--spacing-xs);
    position: relative;
}

.section-subtitle::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: var(--accent-gold);
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: var(--spacing-sm);
    color: var(--black);
    font-weight: var(--fw-bold);
    line-height: 1.2;
}

/* Enhanced section title typography */
[data-lang="en"] .section-title {
    font-family: 'Playfair Display', Georgia, serif;
    font-weight: var(--fw-semibold);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

[data-lang="ar"] .section-title {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-bold);
    line-height: 1.3;
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray);
    max-width: 600px;
    margin: 0 auto;
}

/* ===================================
   ABOUT SECTION STYLES
   =================================== */

.about {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.about-text {
    animation: fadeInLeft 0.8s ease forwards;
}

.about-story h3 {
    color: var(--primary-brown);
    margin-bottom: var(--spacing-md);
}

.about-story p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: var(--spacing-lg);
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.feature-item {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-brown);
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--elegant-shadow);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: var(--accent-gold);
    border-radius: var(--radius-full);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-item:hover .feature-icon::before {
    opacity: 1;
}

.feature-item:hover .feature-icon {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--elegant-shadow);
}

.feature-content h4 {
    color: var(--black);
    margin-bottom: var(--spacing-xs);
}

.feature-content p {
    color: var(--gray);
    font-size: 0.95rem;
}

.about-visual {
    position: relative;
    animation: fadeInRight 0.8s ease forwards;
}

.about-image-grid {
    position: relative;
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: var(--spacing-md);
    height: 500px;
}

.about-image {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.about-image:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.about-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.main-image {
    grid-row: 1 / 3;
}

.secondary-image {
    grid-column: 2;
    grid-row: 1;
}

.about-stats {
    grid-column: 2;
    grid-row: 2;
    background: var(--primary-brown);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: var(--spacing-md);
    color: var(--white);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: var(--fw-bold);
    font-family: var(--font-primary);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* ===================================
   MENU SECTION STYLES
   =================================== */

.menu {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.menu-filter {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--white);
    color: var(--gray);
    border: 2px solid transparent;
    border-radius: var(--radius-xl);
    font-weight: var(--fw-medium);
    cursor: pointer;
    transition: var(--transition-normal);
}

.filter-btn {
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-brown);
    transition: left 0.3s ease;
    z-index: -1;
}

.filter-btn:hover::before,
.filter-btn.active::before {
    left: 0;
}

.filter-btn:hover,
.filter-btn.active {
    color: var(--white);
    border-color: var(--primary-brown);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--elegant-shadow);
}

.menu-search {
    max-width: 400px;
    margin: 0 auto var(--spacing-xl);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 50px;
    border: 2px solid var(--brown-medium);
    border-radius: var(--radius-xl);
    font-size: 1rem;
    background: var(--white);
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-brown);
    box-shadow: 0 0 0 3px var(--brown-light);
}

.search-btn {
    position: absolute;
    right: 8px;
    background: var(--primary-brown);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.search-btn:hover {
    transform: scale(1.05);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.menu-item {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(20px);
    border: 1px solid var(--elegant-border);
    position: relative;
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-brown);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: left;
    z-index: 1;
}

.menu-item:hover::before {
    transform: scaleX(1);
}

.menu-item.show {
    opacity: 1;
    transform: translateY(0);
}

.menu-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px var(--elegant-shadow);
    border-color: var(--soft-glow);
}

.menu-item-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.menu-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.menu-item:hover .menu-item-image img {
    transform: scale(1.05);
}

.menu-item-content {
    padding: var(--spacing-lg);
}

.menu-item-content h3 {
    color: var(--black);
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
    font-weight: var(--fw-semibold);
}

/* Enhanced menu item typography */
[data-lang="en"] .menu-item-content h3 {
    font-family: 'Playfair Display', Georgia, serif;
    font-weight: var(--fw-semibold);
    font-size: 1.25rem;
    letter-spacing: -0.01em;
}

[data-lang="ar"] .menu-item-content h3 {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--fw-bold);
    font-size: 1.2rem;
}

.menu-item-content p {
    color: var(--gray);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.menu-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-price {
    font-size: 1.2rem;
    font-weight: var(--fw-bold);
    color: var(--deep-green);
    background: var(--green-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
}

.menu-cta {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* ===================================
   GALLERY SECTION STYLES
   =================================== */

.gallery {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.gallery-filter {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.gallery-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-normal);
    opacity: 0;
    transform: scale(0.8);
}

.gallery-item.show {
    opacity: 1;
    transform: scale(1);
}

.gallery-item:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(166, 108, 66, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: var(--white);
    font-size: 2rem;
    transform: scale(0.5);
    transition: var(--transition-normal);
}

.gallery-item:hover .gallery-overlay i {
    transform: scale(1);
}

/* ===================================
   BRANCHES SECTION STYLES
   =================================== */

.branches {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.branches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.branch-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--elegant-border);
    position: relative;
}

.branch-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--deep-green);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: left;
    z-index: 1;
}

.branch-card:hover::before {
    transform: scaleX(1);
}

.branch-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(33, 99, 84, 0.15);
    border-color: rgba(33, 99, 84, 0.25);
}

.branch-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.branch-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.branch-overlay {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
}

.branch-status {
    background: var(--deep-green);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    font-weight: var(--fw-semibold);
}

.branch-content {
    padding: var(--spacing-lg);
}

.branch-name {
    color: var(--black);
    margin-bottom: var(--spacing-md);
}

.branch-info {
    margin-bottom: var(--spacing-lg);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--gray);
}

.info-item i {
    color: var(--primary-brown);
    width: 20px;
}

.branch-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.branch-actions .btn {
    flex: 1;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* ===================================
   CONTACT SECTION STYLES
   =================================== */

.contact {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.contact-item {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--deep-green);
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(33, 99, 84, 0.15);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: var(--accent-gold);
    border-radius: var(--radius-full);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-item:hover .contact-icon::before {
    opacity: 1;
}

.contact-item:hover .contact-icon {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(33, 99, 84, 0.2);
}

.contact-details h4 {
    color: var(--black);
    margin-bottom: var(--spacing-xs);
}

.contact-details p {
    color: var(--gray);
    margin-bottom: var(--spacing-xs);
}

.contact-social {
    margin-top: var(--spacing-md);
}

.contact-social h4 {
    margin-bottom: var(--spacing-md);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--light-gray);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-brown);
    transition: left 0.3s ease;
    z-index: -1;
}

.social-link:hover::before {
    left: 0;
}

.social-link:hover {
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px var(--elegant-shadow);
}

/* Specific social media colors on hover */
.social-link[href*="facebook"]:hover::before {
    background: #1877F2;
}

.social-link[href*="instagram"]:hover::before {
    background: #E4405F;
}

.social-link[href*="twitter"]:hover::before {
    background: #1DA1F2;
}

.social-link[href*="whatsapp"]:hover::before {
    background: #25D366;
}

/* ===================================
   FORM STYLES
   =================================== */

.contact-form {
    background: var(--light-gray);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
}

.form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--primary-brown);
    font-weight: var(--fw-semibold);
    font-size: 0.95rem;
    font-family: var(--font-secondary);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--elegant-border);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--white);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: var(--font-secondary);
    color: var(--black);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-brown);
    box-shadow: 0 0 0 3px var(--elegant-shadow);
    transform: translateY(-1px);
}

.form-input:hover {
    border-color: var(--soft-glow);
}

.form-input::placeholder {
    color: var(--gray);
    opacity: 0.7;
    font-style: italic;
}

textarea.form-input {
    min-height: 120px;
    resize: vertical;
    font-family: var(--font-secondary);
}

.form-submit {
    align-self: flex-start;
    margin-top: var(--spacing-md);
}

/* ===================================
   FOOTER STYLES
   =================================== */

.footer {
    background: var(--black);
    color: var(--white);
    padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.footer-logo img {
    width: 40px;
    height: 40px;
}

.footer-logo span {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: var(--fw-bold);
    color: var(--primary-brown);
}

.footer-description {
    color: var(--gray);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.footer-links a {
    color: var(--gray);
    text-decoration: none;
    transition: var(--transition-normal);
}

.footer-links a:hover {
    color: var(--primary-brown);
}

.footer-contact p {
    color: var(--gray);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-contact i {
    color: var(--primary-brown);
    width: 20px;
}

.newsletter-form {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.newsletter-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray);
    border-radius: var(--radius-md);
    background: var(--white);
    font-size: 0.9rem;
}

.newsletter-btn {
    background: var(--primary-brown);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
}

.newsletter-btn:hover {
    transform: translateY(-2px);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray);
}

.footer-copyright p {
    color: var(--gray);
    font-size: 0.9rem;
}

.footer-social {
    display: flex;
    gap: var(--spacing-sm);
}

/* ===================================
   LIGHTBOX STYLES
   =================================== */

.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--radius-md);
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: var(--white);
    font-size: 2rem;
    cursor: pointer;
    transition: var(--transition-normal);
}

.lightbox-close:hover {
    color: var(--primary-brown);
}

.lightbox-prev,
.lightbox-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(166, 108, 66, 0.8);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.lightbox-prev {
    left: -60px;
}

.lightbox-next {
    right: -60px;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: var(--primary-brown);
    transform: translateY(-50%) scale(1.1);
}

.lightbox-caption {
    position: absolute;
    bottom: -40px;
    left: 0;
    right: 0;
    text-align: center;
    color: var(--white);
    font-size: 1rem;
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Large Tablet Styles */
@media (max-width: 1200px) {
    .container {
        max-width: 1000px;
        padding: 0 var(--spacing-lg);
    }

    .hero-content {
        gap: var(--spacing-xl);
    }

    .menu-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}



/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }



    /* Hero adjustments for tablet */
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .hero-image {
        max-width: 400px;
        margin: 0 auto;
    }

    /* About section for tablet */
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .about-image-grid {
        height: 450px;
        max-width: 500px;
        margin: 0 auto;
    }

    /* Contact section for tablet */
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    /* Branches for tablet */
    .branches-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: var(--spacing-xl);
    }

    /* Menu for tablet */
    .menu-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    /* Gallery for tablet */
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    /* Navigation adjustments */
    .nav-menu {
        gap: var(--spacing-md);
    }

    .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.95rem;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Container for mobile */
    .container {
        padding: 0 var(--spacing-md);
    }

    /* Navigation improvements */
    .nav-container {
        padding: 0 var(--spacing-md);
        position: relative;
    }

    .nav-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: var(--spacing-xxl) var(--spacing-lg);
        transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: var(--z-modal);
        overflow-y: auto;
    }

    /* Dark mode for mobile menu */
    [data-theme="dark"] .nav-menu {
        background: rgba(26, 26, 26, 0.98);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }

    .nav-menu.active {
        left: 0;
    }

    /* RTL support for mobile menu */
    [data-lang="ar"] .nav-menu {
        left: -100%;
    }

    [data-lang="ar"] .nav-menu.active {
        left: 0;
    }

    [data-lang="en"] .nav-menu {
        right: -100%;
        left: auto;
    }

    [data-lang="en"] .nav-menu.active {
        right: 0;
        left: auto;
    }

    .nav-item {
        margin: var(--spacing-md) 0;
        opacity: 0;
        transform: translateY(30px) scale(0.9);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        width: 100%;
        max-width: 280px;
    }

    .nav-menu.active .nav-item {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    .nav-menu.active .nav-item:nth-child(1) { transition-delay: 0.1s; }
    .nav-menu.active .nav-item:nth-child(2) { transition-delay: 0.15s; }
    .nav-menu.active .nav-item:nth-child(3) { transition-delay: 0.2s; }
    .nav-menu.active .nav-item:nth-child(4) { transition-delay: 0.25s; }
    .nav-menu.active .nav-item:nth-child(5) { transition-delay: 0.3s; }
    .nav-menu.active .nav-item:nth-child(6) { transition-delay: 0.35s; }

    .nav-link {
        font-size: 1.2rem;
        font-weight: var(--fw-semibold);
        padding: var(--spacing-md) var(--spacing-xl);
        border-radius: var(--radius-lg);
        width: 100%;
        justify-content: center;
        background: rgba(166, 108, 66, 0.05);
        border: 2px solid transparent;
        transition: all 0.3s ease;
        color: var(--black);
        text-align: center;
    }

    /* Language-specific alignment for mobile nav */
    [data-lang="ar"] .nav-link {
        direction: rtl;
    }

    [data-lang="en"] .nav-link {
        direction: ltr;
    }

    .nav-link:hover,
    .nav-link.active {
        background: var(--primary-brown);
        color: var(--white);
        border-color: var(--primary-brown);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    /* Dark mode for mobile nav links */
    [data-theme="dark"] .nav-link {
        background: rgba(166, 108, 66, 0.1);
        color: var(--black);
        border-color: rgba(166, 108, 66, 0.2);
    }

    [data-theme="dark"] .nav-link:hover,
    [data-theme="dark"] .nav-link.active {
        background: var(--primary-brown);
        color: var(--white);
        border-color: var(--primary-brown);
    }

    .nav-toggle {
        display: flex;
        z-index: calc(var(--z-modal) + 1);
        position: relative;
    }

    .nav-cta {
        display: none;
    }





    .theme-toggle {
        order: -1;
        z-index: calc(var(--z-modal) + 1);
        position: relative;
    }

    /* Prevent body scroll when menu is open */
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100%;
    }

    /* Dark mode mobile menu overlay */
    [data-theme="dark"] .nav-menu::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(26, 26, 26, 0.95);
        z-index: -1;
    }

    /* Hero Section Mobile */
    .hero {
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);
        padding-top: 0;
        display: flex;
        align-items: center;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        padding: var(--spacing-xl) var(--spacing-md);
        text-align: center;
        min-height: calc(100vh - 80px);
        min-height: calc(var(--vh, 1vh) * 100 - 80px);
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .hero-text {
        order: 1;
    }

    .hero-image {
        order: 2;
        max-width: 300px;
        margin: 0 auto;
    }

    .hero-title {
        margin-bottom: var(--spacing-lg);
    }

    .title-line {
        font-size: 1rem;
        margin-bottom: var(--spacing-xs);
    }

    .title-main {
        font-size: clamp(2.5rem, 8vw, 4rem);
        margin-bottom: var(--spacing-sm);
    }

    .title-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: var(--spacing-xl);
        max-width: 100%;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .btn {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: 1rem;
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    /* Sections Mobile */
    .section-header {
        margin-bottom: var(--spacing-xl);
        padding: 0 var(--spacing-sm);
    }

    .section-title {
        font-size: clamp(1.8rem, 6vw, 2.5rem);
    }

    .section-description {
        font-size: 1rem;
        max-width: 100%;
    }

    /* About Section Mobile */
    .about-image-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
        gap: var(--spacing-md);
    }

    .main-image {
        grid-row: 1;
        height: 250px;
    }

    .secondary-image {
        grid-column: 1;
        grid-row: 2;
        height: 200px;
    }

    .about-stats {
        grid-column: 1;
        grid-row: 3;
        padding: var(--spacing-lg);
        text-align: center;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    /* Menu Section Mobile */
    .menu-filter {
        gap: var(--spacing-xs);
        padding: 0 var(--spacing-sm);
        justify-content: center;
        flex-wrap: wrap;
    }

    .filter-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
        min-width: auto;
        flex: 0 0 auto;
    }

    .filter-btn i {
        display: none;
    }

    .menu-search {
        padding: 0 var(--spacing-md);
    }

    .menu-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        padding: 0 var(--spacing-sm);
    }

    .menu-item {
        max-width: 100%;
    }

    .menu-item-image {
        height: 220px;
    }

    /* Gallery Section Mobile */
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
        padding: 0 var(--spacing-sm);
    }

    /* Branches Section Mobile */
    .branches-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        padding: 0 var(--spacing-sm);
    }

    .branch-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .branch-actions .btn {
        width: 100%;
    }

    /* Contact Section Mobile */
    .contact-content {
        gap: var(--spacing-xl);
    }

    .contact-form {
        padding: var(--spacing-lg);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .lightbox-prev {
        left: 10px;
    }

    .lightbox-next {
        right: 10px;
    }

    .lightbox-close {
        top: 10px;
        right: 10px;
    }

    .lightbox-caption {
        bottom: 10px;
        padding: 0 var(--spacing-md);
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    /* Navigation for very small screens */
    .nav-container {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .logo-text {
        font-size: 1.3rem;
    }

    .logo-image {
        width: 35px;
        height: 35px;
    }

    /* Hero for very small screens */
    .hero-content {
        padding: var(--spacing-lg) var(--spacing-sm);
        gap: var(--spacing-lg);
    }

    .title-main {
        font-size: clamp(2rem, 10vw, 3rem);
    }

    .hero-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .btn {
        width: 100%;
        max-width: 280px;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 0.95rem;
    }

    /* Sections for very small screens */
    .section-header {
        padding: 0 var(--spacing-xs);
        margin-bottom: var(--spacing-lg);
    }

    .section-title {
        font-size: clamp(1.5rem, 7vw, 2rem);
    }

    .section-subtitle {
        font-size: 0.8rem;
    }

    /* Menu for very small screens */
    .menu-filter {
        padding: 0 var(--spacing-xs);
        gap: var(--spacing-xs);
    }

    .filter-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }

    .menu-search {
        padding: 0 var(--spacing-sm);
    }

    .menu-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: 0 var(--spacing-xs);
    }

    .menu-item-content {
        padding: var(--spacing-md);
    }

    /* Gallery for very small screens */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: 0 var(--spacing-xs);
    }

    /* About section for very small screens */
    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Contact for very small screens */
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .contact-form {
        padding: var(--spacing-md);
    }

    /* Footer for very small screens */
    .footer-content {
        gap: var(--spacing-lg);
        text-align: center;
    }

    .newsletter-form {
        flex-direction: column;
        gap: var(--spacing-sm);
    }


}

/* ===================================
   LANGUAGE SUPPORT
   =================================== */

/* English Language Styles */
[data-lang="en"] {
    direction: ltr;
    text-align: left;
}

[data-lang="en"] .nav-menu {
    flex-direction: row;
}

[data-lang="en"] .nav-item {
    margin: 0 var(--spacing-xs);
}

[data-lang="en"] .hero-text {
    text-align: left;
}

[data-lang="en"] .section-header {
    text-align: left;
}

[data-lang="en"] .footer-links {
    text-align: left;
}

[data-lang="en"] .contact-details {
    text-align: left;
}

/* Form labels now use standard positioning */

[data-lang="en"] .lightbox-prev {
    left: -60px;
    right: auto;
}

[data-lang="en"] .lightbox-next {
    right: -60px;
    left: auto;
}

[data-lang="en"] .lightbox-prev i::before {
    content: "\f053"; /* fa-chevron-left */
}

[data-lang="en"] .lightbox-next i::before {
    content: "\f054"; /* fa-chevron-right */
}

/* Mobile adjustments for English */
@media (max-width: 768px) {
    [data-lang="en"] .nav-menu {
        flex-direction: column;
    }

    [data-lang="en"] .nav-item {
        margin: var(--spacing-sm) 0;
    }

    [data-lang="en"] .hero-content {
        text-align: center;
    }

    [data-lang="en"] .section-header {
        text-align: center;
    }

    [data-lang="en"] .lightbox-prev {
        left: 10px;
    }

    [data-lang="en"] .lightbox-next {
        right: 10px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .mobile-cta,
    .lightbox,
    .loading-screen {
        display: none !important;
    }

    .hero {
        min-height: auto;
        page-break-after: always;
    }

    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}
